# PostgreSQL. Versions 8.2 and up are supported.
#
# Install the pg driver:
#   gem install pg
# On OS X with Homebrew:
#   gem install pg -- --with-pg-config=/usr/local/bin/pg_config
# On OS X with MacPorts:
#   gem install pg -- --with-pg-config=/opt/local/lib/postgresql84/bin/pg_config
# On Windows:
#   gem install pg
#       Choose the win32 build.
#       Install PostgreSQL and put its /bin directory on your path.
#
# Configure Using Gemfile
# gem 'pg'
#
default: &default
  adapter: postgresql
  encoding: unicode
  # For details on connection pooling, see rails configuration guide
  # http://guides.rubyonrails.org/configuring.html#database-pooling
  pool: <%= ENV["DB_POOL"] || 5 %>
  prepared_statements: false

development:
  <<: *default
  database: mirraw_dev_beta
  username: <%= ENV['MIRRAW_MOBILE_DEV_DATABASE_USERNAME'] %>
  password: <%= ENV['MIRRAW_MOBILE_DEV_DATABASE_PASSWORD'] %>
  prepared_statements: false


staging:
  <<: *default
  database: <%= ENV['MIRRAW_MOBILE_DEV_DATABASE'] %>
  username: <%= ENV['MIRRAW_MOBILE_DEV_DATABASE_USERNAME'] %>
  password: <%= ENV['MIRRAW_MOBILE_DEV_DATABASE_PASSWORD'] %>
  prepared_statements: false

  # The specified database role being used to connect to postgres.
  # To create additional roles in postgres see `$ createuser --help`.
  # When left blank, postgres will use the default role. This is
  # the same name as the operating system user that initialized the database.
  #username: mirraw_mobile

  # The password associated with the postgres role (username).
  #password:

  # Connect on a TCP socket. Omitted by default since the client uses a
  # domain socket that doesn't need configuration. Windows does not have
  # domain sockets, so uncomment these lines.
  #host: localhost

  # The TCP port the server listens on. Defaults to 5432.
  # If your server runs on a different port number, change accordingly.
  #port: 5432

  # Schema search path. The server defaults to $user,public
  #schema_search_path: myapp,sharedapp,public

  # Minimum log levels, in increasing order:
  #   debug5, debug4, debug3, debug2, debug1,
  #   log, notice, warning, error, fatal, and panic
  # Defaults to warning.
  #min_messages: notice

# Warning: The database defined as "test" will be erased and
# re-generated from your development database when you run "rake".
# Do not set this db to the same as development or production.
test:
  <<: *default
  database: mirraw_dev_be
  prepared_statements: false

# As with config/secrets.yml, you never want to store sensitive information,
# like your database password, in your source code. If your source code is
# ever seen by anyone, they now have access to your database.
#
# Instead, provide the password as a unix environment variable when you boot
# the app. Read http://guides.rubyonrails.org/configuring.html#configuring-a-database
# for a full rundown on how to provide these environment variables in a
# production deployment.
#
# On Heroku and other platform providers, you may have a full connection URL
# available as an environment variable. For example:
#
#   DATABASE_URL="postgres://myuser:mypass@localhost/somedatabase"
#
# You can use this database configuration with:
#
#   production:
#     url: <%= ENV['DATABASE_URL'] %>
#
production:
  <<: *default
  database: <%= ENV['MIRRAW_MOBILE_PROD_DATABASE'] %>
  username: <%= ENV['MIRRAW_MOBILE_PROD_DATABASE_USERNAME'] %>
  password: <%= ENV['MIRRAW_MOBILE_PROD_DATABASE_PASSWORD'] %>
  prepared_statements: false
  host: <%= ENV['DATABASE_URL'] %>
