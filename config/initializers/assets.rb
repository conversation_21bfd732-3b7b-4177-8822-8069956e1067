# Be sure to restart your server when you modify this file.

# Version of your assets, change this if you want to expire all your assets.
Rails.application.config.assets.version = '1.0'

# Add additional assets to the asset load path
Rails.application.config.assets.paths << "#{Rails.root}/app/assets/fonts"
Rails.application.config.assets.paths << Rails.root.join('vendor', 'assets', 'fonts')  
# Precompile additional assets.
Rails.application.config.assets.gzip = false
# application.js, application.css, and all non-JS/CSS in app/assets folder are already added.

Rails.application.config.assets.precompile << /\.(?:svg|eot|woff|woff2|ttf)$/
Rails.application.config.assets.precompile += %w( product_video_controls.* designs.* store.* autosuggest.* unbxdAutosuggest.* countdown.min.* unbxd_autosuggest.* handlebars.js foundation_and_overrides.* carts.* is_in_viewport.js home.* store.css sessions.css addresses.* orders.* guest_login.js lightslider.* opera_mini_fix.* slider_opera.js catalog.* user_profile.* horoscopes.* surveys.* social-share.* pages.* offers.css awesomplete.* fashion_updates.* landing.css returns.* reviews.* jquery.raty.js coupons.css unbxd_recommendations.* survey_question.* dynamic_landing_page.css stitching_measurement.* measurements.js pnotify.custom.min.* wallet.* sitemaps.css subscription_mobile.js slick.*)

Rails.application.config.assets.precompile += %w( inline_header.css application_black.css application_critical_white.css application_black_critical.css designs_black.css store_black.css foundation_and_overrides_black.css carts_black.css home_black.css sessions_black.css addresses_black.css orders_black.css catalog_black.css user_profile_black.css horoscopes_black.css pages_black.css offers_black.css fashion_updates_black.css landing_black.css returns_black.css reviews_black.css coupons_black.css unbxd_recommendations_black.css wallet.* sitemaps_black.css)

Rails.application.config.assets.precompile += %w( rating_review.css inline_header_red.css application_red.css application_black_critical_red.css designs_red.css foundation_and_overrides_red.css carts_red.css coupon_red.css home_red.css sessions_red.css addresses_red.css orders_red.css catalog_red.css pages_red.css offers_red.css returns_red.css reviews_red.css coupons_red.css unbxd_recommendations_red.css wallet.* sitemaps_black.css autosuggest_red.css wallet_red.css stitching_measurement_red.css 404_page.css swiper-bundle.min.*)

Rails.application.config.assets.precompile += %w( amp/application.css )

Rails.application.config.assets.precompile += %w(gift_card_orders.css gift_card_orders.js)

Rails.application.config.assets.precompile += %w(selectize.js)

Rails.application.config.assets.precompile += %w( stripe_checkout.css )
Rails.application.config.assets.precompile += %w( stripe_checkout.js payment.js payments/*.js retry_cod.* request_phone_modal.* sessions.js )

Rails.application.config.assets.precompile += %w( request_phone_number.css video_listing.css login_revamped.*)
Rails.application.config.assets.precompile += %w( product.js product_recommendation.js)
Rails.application.config.assets.precompile += %w( ordersJS.js paypal_client.js accordian.js int_payment_options.js)
Rails.application.config.assets.precompile += %w( ga_payment_info.js)
Rails.application.config.assets.precompile += %w( login.js stitching_video.js pushengage.js)
Rails.application.config.assets.precompile += %w( catalog/*.js)
Rails.application.config.assets.precompile += %w( blaze.js designer.js)
Rails.application.config.assets.precompile += %w( bulk_order_inquiry.scss )
Rails.application.config.assets.precompile += %w( product_carts.js)
