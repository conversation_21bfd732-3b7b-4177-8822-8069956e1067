#NOTE: This File should be used for setting constants in application

DESIGNER_ADDON = (ENV['DESIGNER_ADDON'].to_i == 1) || Rails.env.development?
VENDOR_ADDON_CATEGORY = SystemConstant.get('VENDOR_ADDON_CATEGORY').split(',')
API_CACHE_LIFESPAN = (ENV['API_CACHE_LIFESPAN'] || 60).to_i
VALID_API_TOKENS = ApiUser.where(blocked: false).where.not(device_type: 'search_only').pluck(:token)
JABBER_REVIEW_ENABLED = (ENV['JABBER_REVIEW_ENABLED'].to_i == 1)
DOMESTIC_SHIPPING_TIME = SystemConstant.get('DOMESTIC_SHIPPING_TIME').to_i || 8
INTERNATIONAL_SHIPPING_TIME = SystemConstant.get('INTERNATIONAL_SHIPPING_TIME').to_i || 12
DOMESTIC_SHIPPING_CHARGES = (JSON.parse(SystemConstant.get('DOMESTIC_SHIPPING_CHARGES')).try(:sort_by) {|total,shipping_charge|total.to_i}).try(:to_h) || {'500'=> 50}
DOMESTIC_PREPAID_SHIPPING_PROMOTION = (ENV['DOMESTIC_SHIPPING_PROMOTION'] == 'true')
DESIGN_POLICIES_SHIPPING_IN_INDIA_CHARGES = DOMESTIC_SHIPPING_CHARGES.blank? ? "FREE shipping on all orders" : "FREE shipping on orders above ₹ #{DOMESTIC_SHIPPING_CHARGES.keys.last}"
ESSENTIAL_DESIGNERS = JSON.parse(SystemConstant.get('ESSENTIAL_DESIGNERS') || {})
DESIGN_POLICIES_SHIPPING_IN_INDIA_CHARGES = "" if ESSENTIAL_DESIGNERS["total_below_x"] <= 0
CUSTOM_DUTY_COUNTRY = JSON.parse(SystemConstant.get('CUSTOM_DUTY_COUNTRY'))

ENABLE_PIPELINE_SORT = (ENV['ENABLE_PIPELINE_SORT'].to_i == 1)

IMAGE_PROTOCOL = ENV['HTTP2_ENABLE'] == 'true' ? 'https://' : '//'
SITE_PROTOCOL = ENV['HTTP2_ENABLE'] == 'true' ? 'https://' : '//'

#COD_OTP_DISABLE SWITCH
COD_OTP_DISABLED = (ENV['COD_OTP_DISABLED'].to_i == 1)

MIRRAW_CONTACT_INFO = '+91-2266484300'

RANDOM_FILTER_COUNT_ACTIVATED = (ENV['RANDOM_FILTER_COUNT_ACTIVATED'] == 'true')
INHOUSE_VENDOR_IDS = SystemConstant.get('INHOUSE_VENDOR_IDS').to_s.split(',')
STITCHING_ENABLE = SystemConstant.get('STITCHING_ENABLE')
COD_REQUEST_ENABLE = SystemConstant.get('COD_REQUEST_ENABLE')
DIAL2VERIFY_COD_API = "http://engine.dial2verify.in/Integ/API.dvf?mobile={phone}&passkey={z2v_token}&notify={notify_host_url}&e-notify=<EMAIL>&out=JSON&cn=IN"

ORDER_RETURN_REASONS = {
  'Wrong Size' => ['Size Is Too Small ','Size Is Too Large'],
  'Wrong Product' => nil,
  'Damaged Product' => nil,
  'Print /Design /Color Mismatch' => nil,
  'Quality Issue' => ['Product Was Dirty And Had Stains','Used Product Was Delivered','Bad Fabric/Poor Material','Quality Of Product Not As Per My Expectations']
} #assigned boolean values to check whether image is req. during creating return or not

RETURN_REFUND_TYPE = ['Replacement','Coupon','Refund', 'Wallet']

US_COUNTRY = 'united states'
APP_SOURCE = %w{mobile android ios}
APPSEE_ACTIVATED = SystemConstant.get('APPSEE_ACTIVATED')
UNINSTALLIO_ACTIVATED = SystemConstant.get('UNINSTALLIO_ACTIVATED')
DELAYED_MINUTES = SystemConstant.get('DELAYED_MINUTES').to_i

PAY_WITH_PAYTM_DISABLED = ( ENV['PAY_WITH_PAYTM_DISABLED'].to_i == 1)
DYNAMIC_PRICE_ENABLED = ENV["DYNAMIC_PRICE_ENABLED"].to_i==1
SWITCH_DP_TO_INTERNATIONAL = SystemConstant.get('DYNAMIC_PRICE_INT').to_i == 1
PAY_WITH_AMAZON_DISABLED =(ENV["PAY_WITH_AMAZON_DISABLED"].to_i == 1)

MOBILE_CHECKOUT_PERCENTAGE = SystemConstant.get('MOBILE_CHECKOUT_PERCENTAGE').to_i

ALLOWED_APP_VERSIONS = SystemConstant.get('ALLOWED_APP_VERSIONS_ANDROID').split(',')
ALLOWED_IOS_APP_VERSIONS = SystemConstant.get('ALLOWED_APP_VERSIONS_IOS').split(',')
WALLET_PAYMENT = 'Mirraw Wallet'
REFERRAL_MESSAGE = 'Refer your friends and earn rewards on each referral.'
PLUS_SIZE = JSON.parse(SystemConstant.get('PLUS_SIZE') || {}.to_json)

# for password encryption/decryption
ALPHANUM = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
ENCODING_KEY = "X0vamj983C1pxwyARK5nuiDblLsZFUke47gozH6hVYPWcIfqSdrGBtN2JMTEOQ"
#for unbxd links
UNBXD_URL_EXT = YAML.load_file("#{Rails.root}/db/data/unbxd_recommendation.yml")
REFERRALS_ACTIVE_COUNTRIES = SystemConstant.get('REFERRALS_ACTIVE_COUNTRIES').split(',')
WELCOME_OFFER_COUNTRIES = SystemConstant.get('WELCOME_OFFER_COUNTRIES').split(',')
INFO_MESSAGE = SystemConstant.get('INFO_MESSAGE')
SALWAR_KAMEEZ_FIELDS = YAML.load_file("#{Rails.root.to_s}/db/data/salwar_kameez.yml")
LEHENGA_FIELDS = YAML.load_file("#{Rails.root.to_s}/db/data/lehenga.yml")
MEASUREMENTS =  YAML.load_file("#{Rails.root.to_s}/db/data/measurements.yml")
HELP_CENTER_TABLE = YAML.load_file("#{Rails.root}/db/data/help_center.yml")
API_HELP_CENTER_TABLE = YAML.load_file("#{Rails.root}/db/data/api_help_center.yml")
COLOR_CODES = YAML.load_file("#{Rails.root}/db/data/color_codes.yml")
FRESHDESK_LINK = "https://mirraw.freshdesk.com/support/solutions/articles/3000014735-what-is-cash-before-delivery"
REFERRAL_TYPES = REFERRAL_TYPE.split(',')
MIN_CART_VALUE_ON_HEADER = JSON.parse(SystemConstant.get('MIN_CART_VALUE_ON_HEADER'))
API_DEVICE_ID = SystemConstant.get('WEB_DEVICE_ID')
API_TOKEN = ENV.fetch('API_TOKEN')
AUTOCOMPLETE_ZIPCODE = YAML.load_file "config/autocomplete_zipcode.yml"
AUTOCOMPLETE_ZIPCODE_ACTIVATED = SystemConstant.get('AUTOCOMPLETE_ZIPCODE_ACTIVATED') == 'true'
PINCODE_FORMATS = {
               'India' => '400001',
               'Afghanistan' => '1008',
               'Albania' => '3500',
               'Algeria' => '16101',
               'American Samoa' => '96799',
               'Andorra' => 'AD200',
               'Angola' => '46703',
               'Anguilla' => 'AI-2640',
               'Antigua And Barbuda' => '',
               'Argentina' => '3749',
               'Armenia' => '0002',
               'Aruba' => '',
               'Australia' => '2060',
               'Austria' => '1010',
               'Azerbaijan' => 'AZ 1000 ',
               'Bahamas' => '',
               'Bahrain' => '317',
               'Bangladesh' => '1340',
               'Barbados' => 'BB26028',
               'Belarus' => '220005',
               'Belgium' => '2610',
               'Belize' => '',
               'Benin' => '',
               'Bermuda' => 'CR 03',
               'Bhutan' => '11001',
               'Bolivia' => '',
               'Bosnia and Herzegovina' => '71000',
               'Botswana' => '',
               'Brazil' => '40015-360',
               'British Virgin Islands' => 'VG1110',
               'Brunei' => 'BT2328',
               'Bulgaria' => '4000',
               'Burkina Faso' => '',
               'Burma' => '',
               'Burundi' => '',
               'Cambodia' => '12203',
               'Cameroon' => '',
               'Canada' => 'H3Z 2Y7',
               'Cape Verde' => '7600',
               'Cayman Islands' => 'KY1-1100',
               'Central African Republic' => '',
               'Chad' => '',
               'Chile' => '8720019',
               'China' => '100004',
               'Colombia' => '111711',
               'Comoros' => '',
               'Cook Islands' => '',
               'Costa Rica' => '40501',
               'Croatia' => '10000',
               'Cuba' => '10600',
               'Cyprus' => '2008',
               'Czech Republic' => '100 00 ',
               'Denmark' => '8660',
               'Djibouti' => '',
               'Dominica' => '',
               'Dominican Republic' => '11903',
               'East Timor' => '',
               'Ecuador' => '090514',
               'Egypt' => '12411',
               'El Salvador' => '1120',
               'Equatorial Guinea' => '',
               'Eritrea' => '',
               'Estonia' => '69501',
               'Ethiopia' => '1000',
               'Falkland Islands' => 'FIQQ 1ZZ',
               'Faroe Islands' => '927',
               'Fiji' => '',
               'Finland' => '00550',
               'France' => '67000',
               'French Guiana' => '97300',
               'French Polynesia' => '98709',
               'Gabon' => '',
               'Gambia' => '',
               'Georgia' => '0100',
               'Germany' => '26133',
               'Ghana' => '',
               'Gibraltar' => 'GX11 1AA',
               'Greece' => '151 24',
               'Greenland' => '3900',
               'Grenada' => '',
               'Guadeloupe' => '97100',
               'Guam' => '96910',
               'Guatemala' => '01004',
               'Guinea' => '001',
               'Guinea-Bissau' => '1000',
               'Guyana' => '97312',
               'Haiti' => '6120',
               'Honduras' => 'CM1102',
               'Hungary' => '1037',
               'Iceland' => '220',
               'Indonesia' => '40198',
               'Iran, Islamic Republic Of' => '1193653471',
               'Iraq' => '61002',
               'Ireland' => '',
               'Israel' => '94142',
               'Italy' => '00144',
               'Jamaica' => '',
               'Japan' => '100-0001',
               'Jordan' => '11937',
               'Kazakhstan' => '040900',
               'Kenya' => '20100',
               'Kiribati' => '',
               'Kuwait' => '14000',
               'Kyrgyzstan' => '720001',
               "Lao People's Democratic Republic" => '01160',
               'Latvia' => 'LV-1073',
               'Lebanon' => '2038 3054',
               'Lesotho'=> '100',
               'Liberia' => '1000',
               'Libya' => '',
               'Liechtenstein' => '2544',
               'Lithuania' => 'LT-00104',
               'Luxembourg' => 'L-1025',
               'Macau' => '',
               'Macedonia' => '1000',
               'Madagascar' => '501',
               'Malawi' => '',
               'Malaysia' => '50000',
               'Maldives' => '20026',
               'Mali' => '91091',
               'Malta' => 'ZTN 1073',
               'Marshall Islands' => '96960',
               'Martinique' => '97200',
               'Mauritania' => '',
               'Mauritius' => '42602',
               'Mayotte' => '97633',
               'Mexico' => '02860',
               'Micronesia, Federated States Of' => '96941',
               'Moldova, Republic of' => 'MD-2012',
               'Monaco' => '98000',
               'Mongolia' => '15141',
               'Montserrat' => 'MSR1350',
               'Morocco' => '53000',
               'Mozambique' => '1100',
               'Namibia' => '26890',
               'Nauru' => '',
               'Nepal' => '44601',
               'Netherlands' => 'NL-1000',
               'Netherlands Antilles' => '',
               'New Caledonia' => '98814',
               'New Zealand' => '6011',
               'Nicaragua' => '12005',
               'Niger' => '8001',
               'Nigeria' => '930283',
               'Niue' => '6011',
               'Norfolk Island' => '2899',
               'Northern Mariana Islands' => '96950',
               'Norway' => '0025',
               'Oman' => '133',
               'Pakistan' => '44000',
               'Palau' => '96939',
               'Panama' => '32401',
               'Papua new Guinea' => '111',
               'Paraguay' => '1536',
               'Peru' => '15074',
               'Philippines' => '2821',
               'Pitcairn' => 'PCRN 1ZZ',
               'Poland' => '00-950',
               'Portugal' => '2725',
               'Puerto Rico' => '00601',
               'Qatar' => '',
               'Runion' => '97400',
               'Romania' => '010067',
               'Rwanda' => '',
               'Saint Helena' => '94574',
               'Saint Kitts And Nevis' => '',
               'Saint Lucia' => 'LC03 101',
               'Saint Vincent and the Grenedines' => 'VC1410',
               'Samoa' => 'WS1190',
               'San Marino' => '47890',
               'Sao Tome and Principe' => '',
               'Saudi Arabia' => '12643-2121',
               'Senegal' => '12500',
               'Serbia' => '456769',
               'Seychelles' => '',
               'Sierra Leone' => '',
               'Singapore' => '546080',
               'Slovakia' => '010 01',
               'Slovenia' => '4000',
               'Solomon Islands' => '',
               'Somalia' => 'JH 09010',
               'South Africa' => '0043',
               'Spain' => '45012',
               'Sri Lanka' => '10654',
               'Sudan' => '11111',
               'Suriname' => '',
               'Swaziland' => 'H100',
               'Sweden' => '123 45',
               'Switzerland' => '8090',
               'Syria' => '',
               'Taiwan' => '200',
               'Tajikistan' => '735450',
               'Tanzania' => '11101',
               'Thailand' => '10110',
               'Togo' => '',
               'Tokelau' => '',
               'Tonga' => '',
               'Trinidad And Tobago' => '500234',
               'Tunisia' => '1002',
               'Turkey' => '06101',
               'Turkmenistan' => '744000',
               'Turks and Caicos Islands' => 'TKCA 1ZZ',
               'Tuvalu' => '',
               'U.S. Virgin Islands' => '00850-9802',
               'Uganda' => '',
               'Ukraine' => '15432',
               'United Arab Emirates' => '',
               'United Kingdom' => 'WC2N 4AA',
               'United States' => '20001',
               'Uruguay' => '91000',
               'Uzbekistan' => '140101',
               'Vanuatu' => '',
               'Vatican City - Holy See' => '',
               'Venezuela' => '1010',
               'Vietnam' => '116806',
               'West Bank' => '',
               'Western Sahara' => '',
               'Yemen' => '',
               'Yugoslavia - Federal Repubic Of' => '',
               'Zambia' => '23456',
               'Zimbabwe' => '',
               'Bouvet Island'=>'',
               'Heard and McDonald Islands' =>'',
               'Antigua and Barbuda' => '',
               'Antarctica'=>'',
               'Brunei Darussalam'=>'KF1338',
               'Cocos (Keeling) Islands'=>'6799',
               'Congo'=>'',
               "Cte D'Ivoire" => '',
               'Curaao'=>'',
               'Guernsey'=>'GY68NW',
               'Hong Kong'=>'',
               'Isle of Man'=>'IM9',
               'Jersey'=>'JE2',
               'Saint Martin'=>'97150',
               'Myanmar'=>'11142',
               'Russian Federation'=>'125009',
               'Svalbard And Jan Mayen'=>'8099',
               'South Sudan' =>'',
               'Sint Maarten'=>'',
               'Syrian Arab Republic'=>'',
               'Timor-Leste' =>'',
               'Taiwan, Republic Of China'=>'200',
               'United States Minor Outlying Islands'=>'96898',
               'Holy See (Vatican City State)'=>'',
               'Saint Vincent And The Grenedines'=>'VC1410',
               'Virgin Islands, British' =>'VG1110',
               'Virgin Islands, US'=>'00850-9802',
               'land Islands'=>'',
               'Saint Barthlemy' =>'97133',
               'Bonaire, Sint Eustatius and Saba' => '',
               'Congo, The Democratic Republic Of The'=>'',
               'Christmas Island'=>'6798',
               'Falkland Islands (Malvinas)' => 'FIQQ 1ZZ',
               'South Georgia and the South Sandwich Islands'=>'SIQQ 1ZZ',
               'British Indian Ocean Territory'=>'BBND 1ZZ',
               "Korea, Democratic People's Republic Of"=>'',
               'Montenegro'=>'81000',
               'Macedonia, the Former Yugoslav Republic Of'=>'7330',
               'Papua New Guinea'=>'633',
               'Saint Pierre And Miquelon' =>'97500',
               'Palestinian Territory, Occupied'=>'',
               'French Southern Territories'=>'',
               'Trinidad and Tobago' =>'500234',
               'Tanzania, United Republic of'=>'11101',
               'Venezuela, Bolivarian Republic of'=>'1010',
               'Wallis and Futuna'=>'98610',
               'Korea, Republic of' => '42007',
               'UAE' => '',
               'Macao' => ''
             }
# Prices related to stitching
B_REGULAR = INFO_STRIP_PRICES[:atv_blouse_std] || 465
B_CUSTOM = INFO_STRIP_PRICES[:atv_blouse_custom] || 620
ORDERING_OF_FAQ = JSON.parse(SystemConstant.get('ORDERING_OF_FAQ') ||  "{\"Order\":1}")
CANCEL_COD_VISIBLE_HOUR = SystemConstant.get('CANCEL_COD_VISIBLE_HOUR').to_i

SS_STANDARD = INFO_STRIP_PRICES[:atv_salwar_suit_std] ||  899
SS_CUSTOM = INFO_STRIP_PRICES[:atv_salwar_suit_custom] || 1085
L_STANDARD = INFO_STRIP_PRICES[:atv_lehenga_std] || 899
L_CUSTOM = INFO_STRIP_PRICES[:atv_lehenga_custom] || 1085
FNP = INFO_STRIP_PRICES[:atv_fnp] || 100

MOBILE_HOME_PAGE_UNBXD_CATEGORIES = JSON.parse(SystemConstant.get('MOBILE_HOME_PAGE_UNBXD_CATEGORIES'))

MIRRAW_DOMAIN = ENV.fetch('MIRRAW_DOMAIN')

SMS_API = "http://smsapi.24x7sms.com/api_1.0/SendSMS.aspx?EmailID=#{ENV["Email24x7"]}&Password=#{ENV["Password24x7"]}&MobileNo={phone}&SenderID=MIRRAW&ServiceName=TEMPLATE_BASED&Message={template}"

PETTICOAT = INFO_STRIP_PRICES[:atv_petticoat] || 300

SIZE_CHART_TABLE = YAML.load_file("#{Rails.root}/db/data/size_chart.yml")
REVISED_SIZE_CHART_TABLE = YAML.load_file("#{Rails.root}/db/data/revised_size_chart.yml")

DESIGNER_SLUGS = JSON.parse(SystemConstant.get('DESIGNER_SLUGS'))

STITCHING_INFORMATION = YAML.load_file("#{Rails.root}/db/data/stitching_information.yml")

SEO_TOP_CONTENT_ENABLE = SystemConstant.get('SEO_TOP_CONTENT_ENABLE').to_s.split(',')
TOP_CONTENT_ENABLE = SystemConstant.get('TOP_CONTENT_ENABLE').to_s.split(',')

MOBILE_TAB_MENU_PRIORITY_LIST = SystemConstant.get('MOBILE_TAB_MENU_PRIORITY_LIST').to_s.split(',')

ANDROID_SUB_APPS = JSON.parse(SystemConstant.get('ANDROID_SUB_APPS'))

ENABLE_STICKY_AJAX_CART = ENV['ENABLE_STICKY_AJAX_CART'].to_s == 'true'

SearchKeywordBoosting = JSON.parse(SystemConstant.get('SearchKeywordBoosting').presence || '{"keywords": 1.5,"imp_keywords": 3,"max_sell_count_scale": 1.4,"min_sell_count_scale": 1.0}' ).to_h.symbolize_keys

NPS_BIFURCATIONS = JSON.parse(SystemConstant.get('NPS_BIBURCATIONS')) rescue {}

USER_CONFIRMED_ORDER_STATE = ['sane', 'complete', 'confirmed', 'pickedup', 'ready_for_dispatch', 'dispatched', 'delivered']

ACTIVATE_VIZURY_TRACKING = (ENV['ACTIVATE_VIZURY_TRACKING'].to_i == 1)
DOM_GRADE_VARIABLE_MOBILE = SystemConstant.get('DOM_GRADE_VARIABLE_MOBILE')
INT_GRADE_VARIABLE_MOBILE = SystemConstant.get('INT_GRADE_VARIABLE_MOBILE')
DOM_CATEGORY_GRADE_VARIABLE = SystemConstant.get('DOM_CATEGORY_GRADE_VARIABLE')
CATEGORY_SET = JSON.parse(SystemConstant.get('CATEGORY_SET'))
CATEGORY_MOBILE_GRADE = SystemConstant.get('CATEGORY_MOBILE_GRADE').split(',')

MENU_SUPER_TAG = JSON.parse(SystemConstant.get('MENU_SUPER_TAG'))

RATING_ENABLE = (ENV['RATING_ENABLE'].to_s == 'true')
RECENT_REVIEW = (ENV['RECENT_REVIEW'].to_s == 'true')
RECENT_REVIEW_ROLLOUT = SystemConstant.get('RECENT_REVIEW_ROLLOUT')
RAKHI_PRE_ORDER = SystemConstant.get('RAKHI_PRE_ORDER').to_s.split(',')

MOBILE_RATING_THRESHOLD = ENV['MOBILE_RATING_THRESHOLD'].to_i

RETURN_POLICY = YAML.load_file("#{Rails.root}/db/data/return_policy.yml")

MOBILE_SOURCE = (ENV['MOBILE_SOURCE'].to_i == 1)

DESIGN_SPEC_SUB_GROUP = JSON.parse(SystemConstant.get('DESIGN_SPEC_SUB_GROUP'))

PAYU_ENABLE_ON_INTERNATIONAL = ENV['PAYU_ENABLE_ON_INTERNATIONAL'] == 'true'

LAZY_IMAGE_SCROLL = (ENV['LAZY_IMAGE_SCROLL'].to_i == 1)

SHIPPING_ITEM_VALUE = (SystemConstant.get('SHIPPING_ITEM_VALUE') || 200).to_i

BRAINTREE_ACCESS_TOKEN = ENV['BRAINTREE_ACCESS_TOKEN']

WEBP_CONFIGURATION = JSON.parse(SystemConstant.get('WEBP_CONFIGURATION')).collect do |model_name, stime|
                          stime = model_name == 'switch' ? (stime == "true") : (stime.to_s.to_time rescue '')
                          [model_name.freeze, stime.freeze ]
                        end.to_h.freeze rescue {}.freeze

CRITEO_ACCOUNT_ID = JSON.parse(ENV['CRITEO_ACCOUNT_ID'] || '{}')

STAGED_APP = SystemConstant.get('STAGED_APP')

FACETED_URL_KINDS = JSON.parse(SystemConstant.get('FACETED_URL_KINDS') || '{}')
FILTER_PARENT_CATEGORIES = SystemConstant.get('FILTER_PARENT_CATEGORIES')|| []

COLOUR_BASED_SEO = SystemConstant.get('COLOUR_BASED_SEO').split(',')

REMARKETING_CONVERSION_ID = ENV['REMARKETING_CONVERSION_ID']

ADWORD_CONVERSION_TRACKING_IDS = JSON.parse(ENV['ADWORD_CONVERSION_TRACKING_IDS'].presence || '{}')

IOS_DEEP_LINK_PATTERNS = SystemConstant.get('IOS_DEEP_LINK_PATTERNS').split(',')

LOYALTY_VALUES = JSON.parse(SystemConstant.get('LOYALTY_VALUES') || '{}').collect{|key,val| [Range.new(*(key.split('..').collect(&:to_i))), val]}.to_h

LINE_ITEM_SYNC_DESIGN = SystemConstant.get('LINE_ITEM_SYNC_DESIGN') == '1'

ENABLE_COD_CHARGE = SystemConstant.get('ENABLE_COD_CHARGE')
ENABLE_COD_COUNTRIES = SystemConstant.get('ENABLE_COD_COUNTRIES').try(:split,',').presence || ['india']

NEW_THEME_ROLLOUT = JSON.parse(ENV['NEW_THEME_ROLLOUT'] || '{}')

THEME_CSS_FILE_MAPPING = YAML.load_file("#{Rails.root}/config/theme_css_file_mapping.yml")

STITCHING_LABEL_DESIGNABLE_TYPE = SystemConstant.get('STITCHING_LABEL_DESIGNABLE_TYPE').split(',').presence || []

CART_COUPON_CONSTANT = JSON.parse(SystemConstant.get('CART_COUPON_CONSTANT') || '{}')

ANARKALI_CATEGORY_FOR_STANDARD_ADDON = promise{Category.where(name: 'anarkali-salwar-kameez').collect(&:self_and_descendants).flatten.collect(&:id)}

UNVEILING_EFFECT = ENV['UNVEILING_EFFECT'] == 'true'

SOR_READY_TO_SHIP = SystemConstant.get('SOR_READY_TO_SHIP') == 'true'
REFERRAL_EXPIRY_PERIOD = SystemConstant.get('REFERRAL_EXPIRY_PERIOD') || 0.days
RTS_ALLOWED_COUNTRIES = SystemConstant.get('RTS_ALLOWED_COUNTRIES').try(:split,',').presence || []

STORY_COLLECTION_CONSTANT = JSON.parse(SystemConstant.get('STORY_COLLECTION_CONSTANT') || '{}')

HIDDEN_FACETS_FILTERS = JSON.parse(SystemConstant.get('HIDDEN_FACETS_FILTERS') || '{}')
BASE_64_PLACHOLDER_IMAGE = "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7".freeze
CART_ADDON = JSON.parse(SystemConstant.get('CART_ADDON') || '{}')
SHARE_AND_EARN_REWARD = SystemConstant.get('SHARE_AND_EARN_REWARD').to_f
TRANSFER_MODEL_DISCOUNT = 20
IGST = 18
DEFAULT_ADDONS = (SystemConstant.get('DEFAULT_ADDONS') || '').split(',')
PROMOTION_EXCLUDED_ADDON_IDS = (SystemConstant.get('PROMOTION_EXCLUDED_ADDON_IDS') || '').split(',')

STITCHING_TESTIMONIALS = YAML.load_file("#{Rails.root}/config/stitching_testimonials.yaml")

ERROR_404_POPULAR_LINKS = {
  "Saree" => "/store/sarees",
  "Salwar Kameez" => "/salwar-suits/salwar-kameez",
  "Lehenga" => "/store/lehengas",
  "Hijab" => "/islamic-clothing/hijab",
  "Burka" => "/islamic-clothing/burka",
  "Kaftan " => "/islamic-clothing/kaftans",
  "Kurti" => "/women/clothing/kurtas-and-kurtis",
  "Jewellery" => "/store/jewellery",
  "Kids Wear" => "/kids",
  "Mens Clothing" => "/men/clothing",
  "Home Decor " => "/home-decor"
}.freeze

CUSTOM_STITCHING_BUST_SIZE_CONSTRAINTS = JSON.parse(SystemConstant.get('CUSTOM_STITCHING_BUST_SIZE_CONSTRAINTS') || '{}')

FEEDBACK_SAMPLE_FORM_LINE_ITEMS = JSON.parse(SystemConstant.get('FEEDBACK_SAMPLE_FORM_LINE_ITEMS') || '{}')

SHOW_TAILOR_PRODUCT_CONTENT = JSON.parse(SystemConstant.get('SHOW_TAILOR_PRODUCT_CONTENT') || '{}')
ENABLE_PLUS_SIZE = JSON.parse(SystemConstant.get('ENABLE_PLUS_SIZE') || '{}')

WALLET_CONFIG = SystemConstant['WALLET_CONFIG']
IP_BLOCK_LIST = SystemConstant.get('IP_BLOCK_LIST').to_s.split(',') || []
PAYPAL_SMARTPAY_SWITCH = SystemConstant.get('PAYPAL_SMARTPAY_SWITCH').to_i
REVERSE_PICKUP_MINIMUM_VERSION = JSON.parse(SystemConstant.get('REVERSE_PICKUP_MINIMUM_VERSION') || '{}')
PREPAID_PROMOTION_MINIMUM_VERSION = JSON.parse(SystemConstant.get('PREPAID_PROMOTION_MINIMUM_VERSION') || '{}')
JUSPAY_ENABLE = ENV['JUSPAY_ENABLE'].to_i == 1
PAYPAL_PRESENTMENT_CURRENCIES = SystemConstant.get('PAYPAL_PRESENTMENT_CURRENCIES').to_s.split(',') || []
RACK_AUDIT_CONFIG = JSON.parse(SystemConstant.get('RACK_AUDIT_CONFIG') || '{}')
METRO_CITIES = SystemConstant.get('METRO_CITIES').to_s.split(',') || %w(mumbai chennai delhi kolkata hyderabad bangalore)
CITY_BASED_SHIP_TIME = JSON.parse(SystemConstant.get('CITY_BASED_SHIP_TIME') || '{}').symbolize_keys || {metro: 2, non_metro: 5}
BANNED_COUNTRIES = JSON.parse(SystemConstant.get('BANNED_COUNTRIES') || '{}')

COD_MESSAGE_PDP_SHOW = SystemConstant.get('COD_MESSAGE_PDP_SHOW') || 'true'
PREPAID_POPUP_SHOW = SystemConstant.get('PREPAID_POPUP_SHOW') || 'true'
ADMIN_QUEUE_WORKER = SystemConstant.get('ADMIN_QUEUE_WORKER') || 'high_priority_admin'
PINCODE_BASED_CATEGORY_DELIVERY = JSON.parse(SystemConstant.get('PINCODE_BASED_CATEGORY_DELIVERY') || '{}')
PAYPAL_BRAINTREE_MINIMUM_VERSION = JSON.parse(SystemConstant.get('PAYPAL_BRAINTREE_MINIMUM_VERSION') || '{}')
DEFAULT_WAREHOUSE_ADDRESS_ID = SystemConstant.get('DEFAULT_WAREHOUSE_ADDRESS_ID').to_i.nonzero? || 1
SHOW_PHONE_POPUP = SystemConstant.get('SHOW_PHONE_POPUP').to_i
INSCAN_DO_MONTHS = SystemConstant.get('INSCAN_DO_MONTHS').to_i.nonzero? || 4
AUTOMATED_COD_REFUND = JSON.parse(SystemConstant.get('AUTOMATED_COD_REFUND') || '{}')
VIDEO_LISTING_DATA = JSON.parse(SystemConstant.get('VIDEO_LISTING_DATA')) || {}
LANE_FUNCTIONALITY = SystemConstant.get('LANE_FUNCTIONALITY').to_i == 1
NEWLY_ADDED_PRODUCTS_DESIGNER = JSON.parse(SystemConstant.get('NEWLY_ADDED_PRODUCTS'))['designer_ids'].presence || [12727]
NEWLY_ADDED_PRODUCTS_CATEGORY = JSON.parse(SystemConstant.get('NEWLY_ADDED_PRODUCTS'))['category_ids'].presence || [23, 58, 150, 358]
ENABLE_AUTO_SIGNUP_WITH_MOBILE = SystemConstant.get('AUTO_SIGNUP_WITH_MOBILE') == "1" ? true : false
ALLOWED_DOMESTIC_SOR = (SystemConstant.get('ALLOWED_DOMESTIC_SOR') == 'true')
EXCLUDE_FREE_SHIPPING_CATEGORIES = SystemConstant.get('EXCLUDE_FREE_SHIPPING_CATEGORIES').to_s.split(',').map(&:to_i) || []
DESIGNER_LSR = SystemConstant.get('DESIGNER_LSR').to_i || 30
REDIRECT_ROUTE = RedirectRule.pluck(:route, :match).flatten | Menu.pluck(:link).to_a | MenuColumn.pluck(:link).to_a | MenuItem.pluck(:link).to_a
CLASSIQUES_DESIGNER = promise{Designer.find(12727)}
LOCKDOWN_COD_DISABLE_PINCODES = JSON.parse(SystemConstant.get('LOCKDOWN_MAYHEM'))['cod_disable_pincodes'].presence || []
LOCKDOWN_NON_SERVICABLE_PINCODES = JSON.parse(SystemConstant.get('LOCKDOWN_MAYHEM'))['non_servicable_pincodes'].presence || []
 
ENABLE_COD_FOR_DESIGNERS_FOR_UAE = JSON.parse(SystemConstant.get('ENABLE_COD_FOR_DESIGNERS_FOR_UAE')).presence || []
PAYMENT_OFFERS = JSON.parse(SystemConstant.get('PAYMENT_OFFERS').presence || '{}' ) 
ENABLE_WIZGO = JSON.parse(SystemConstant.get('ENABLE_WIZGO')).presence || false
ENABLE_COD_WITH_RANGE_AND_COUNTRIES = JSON.parse(SystemConstant.get('ENABLE_COD_WITH_RANGE_AND_COUNTRIES')).presence
FOOTWEAR_CATEGORY_PARENT_IDS = SystemConstant.get('FOOTWEAR_CATEGORY_PARENT_IDS').to_s.split(',').map(&:to_i)
FOOTWEAR_CATEGORY_IDS = SystemConstant.get('FOOTWEAR_CATEGORY_IDS').to_s.split(',').map(&:to_i)
UNBXD_V2_2023 = SystemConstant.get('UNBXD_V2_2023') == "true"
BLOCK_OTP_NUMBERS_CONFIGURATION = JSON.parse(SystemConstant.get('BLOCK_OTP_NUMBERS_CONFIGURATION') || {}.to_json)
GOKWIK_CONFIG = ENV['GOKWIK_CONFIG'].present? ? JSON.parse(ENV['GOKWIK_CONFIG']) : {"enable_gokwik":true,"enable_cod":false,"risk_flags":["high risk"],"enable_gk_on_api": false}
CURRENCY_CONVERT_TO_USD = JSON.parse(SystemConstant.get('CURRENCY_CONVERT_TO_USD')).presence || {}