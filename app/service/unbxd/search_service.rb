module Unbxd
  class SearchService < Unbxd::BaseService

    SEARCH_QUERY_FIELDS = ["unqiueId", "title", "product_url", "price", "discount_price", "designer", "designer_slug",
                 "category_name", "category_id", "mirraw_certified", "mirraw_recommended", "rating", "designer_rating",
                 "grade", "type", "state", "stock", "discount_percent", "promotion_offer_end_time", "total_review",
                 "likes_count", "ready_to_ship", "premium", "image_urls", "color","in_catalog_one", "all_reviews"]

    def call
      begin
        return {} unless check_valid_params
        @payload = generate_payload
        @unbxd_response = send_unbxd_search_request
        response_with_discount_price
      rescue => error
        ExceptionNotify.sidekiq_delay.notify_exceptions("Exception for unbxd Search"," error message for unbxd Search: #{error}",@payload)
      end
    end

    def response_with_discount_price
      if @unbxd_response.key?('search')
        design_ids = @unbxd_response['search']['designs'].collect{|design| design['id']}
        designs = Design.where(id: design_ids).preload(:designer, :categories)
        discounts = {}
        begin 
          designs.each do |design|
            discounts[design.id] = design.effective_price_currency(1)
          end    
          @unbxd_response['search']['designs'].each do |design|
            design['discount_price'] = discounts[design['id'].to_i]
            design['inr_discount_price'] = discounts[design['id'].to_i]
            design_obj = Design.find design['id'].to_i
            design['discount_percent'] = design_obj.effective_discount(PromotionPipeLine.active_promotions)
            design['price'] = design_obj.price_currency(1)
            design['sor_available'] = design_obj.sor_available?
            design['variant_sor_available'] = design_obj.variants.any? { |v| v.sor_available? }
            design['custom_tags'] = design_obj.custom_tags(@params['country_code'])
            design['all_reviews'] = design_obj.all_reviews(@params['country_code'])
            design['average_rating_domestic'] = design_obj.average_rating_domestic
            design['average_rating_international'] =  design_obj.average_rating_international
          end 
        rescue => error 
          ExceptionNotify.sidekiq_delay.notify_exceptions("Exception for unbxd Search"," error message for unbxd Search: #{error}",@payload) 
        end 
      end
      return @unbxd_response
    end
    
    def generate_payload
      @page = @params['page'].present? ? @params['page'].to_i : 0
      @per_page_products = @params['num_products'].present? ? @params['num_products'].to_i : Unbxd::BaseService::PER_PAGE
      search_query = @params['q'].gsub(/[^A-Za-z0-9 ]+/,' ').strip.downcase
      unbxd_query_params = {
            'q': search_query,
            'rows': @per_page_products,
            'start': @per_page_products * @page,
            'version': 'V2',
            'facet.multiselect': true,
            'fallback':true,
            'uid': @params['uid'],
            'fields': SEARCH_QUERY_FIELDS.join(",")
        }
      unbxd_query_params['sort'] = @params['sort_values'] if @params.key?('sort_values')
      # FILTERS START CHECK HERE
      unbxd_query_params['filter'] = final_filter_keys 
      unbxd_query_params
    end

    

    def final_filter_keys
      final_filter_keys = []
      if @params.key?('facet')
        @params['facet'].each do |facet_key, facet_value|
      
          if facet_value['type'] == 'text'
            filter_values = facet_value['value'].map { |v| "#{facet_key}:\"#{v}\"" }
            final_filter_keys << "(#{filter_values.join(' OR ')})"
          else
            if facet_value['type'] == 'range'
              facet_key = 'discount_price' if facet_key == 'price'
              split = facet_value['value'][0].split(' - ')
              min_range = split[0]
              max_range = split[1]
              final_filter_keys << "#{facet_key}:[#{min_range} TO #{max_range}]"      
            end
          end
        end
      end
      #{0: both, 1: domestic , 2:international}
      catalog_visibility = @params['country_code'] == 'IN' ? 1 : 2          #if in future we go for international , wont need to do changes here for catalog
      final_filter_keys << "in_catalog_one:0 OR in_catalog_one:#{catalog_visibility}"
      return final_filter_keys
    end

    ## generate paginated metadata
    def generate_paginated_metadata(number_of_products=0)
      total_pages = (number_of_products.to_i / @per_page_products).ceil
      basic_info = {
        "results": number_of_products,
        "total_pages": total_pages,
        "previous_page": @page > 0 ? (@page - 1) : nil,
        "next_page": (@page < total_pages - 1) ? (@page + 1) : nil,
        "hex_symbol": "20B9",
        "symbol": "Rs",
        "string_symbol": "",
        "promotion_offer_end_time": nil,
        "promotion_offer_landing": nil
      }
      return basic_info.as_json
    end
    
    def set_facet_fields_positioning(facet, position)
      facet_display_name = get_facet_display_name(facet['facetName'])
      all_values = []
      grouper(facet['values'], 2).each do |chunked|
        each_value = {
          'value': chunked[0],
          'name': chunked[0],
          'count': chunked[1]
        }
        
        each_value['color_code'] = get_color_codes["#{each_value[:value].downcase}"] ||  -1 if facet['facetName'].include?('color')
        all_values << each_value
      end
    
      each_new_facet = {
        'key': facet_display_name,
        'return_key': facet['facetName'],
        'type': 'text',
        'list': all_values,
        'name': facet['displayName'],
        'position': position
      }
      return each_new_facet
    end
    
    
    def set_facet_range_positioning(facet, position)
      facet_display_name = get_facet_display_name(facet['facetName'])
      all_values = []
      grouper(facet['values']['counts'], 2).each do |chunked|
        if facet['facetName'] == 'avg_rating'
          start = chunked[0]
          end_value = facet['values']['gap'].to_f + start.to_f
        else
          start = (chunked[0].to_f).ceil
          end_value = (facet['values']['gap'].to_f + start).ceil
        end
        each_value = {
          'value' => { 'min' => start, 'max' => end_value },
          'name' => "#{start} - #{end_value}",
          'count' => chunked[1]
        }
        all_values << each_value
      end
    
      each_new_facet = {
        'keys' => { 'max' => "max_#{facet_display_name.downcase}", 'min' => "min_#{facet_display_name.downcase}" },
        'return_key' => facet['facetName'],
        'type' => 'range',
        'list' => all_values,
        'name' => facet['displayName'],
        'position' => position
      }
      
      each_new_facet
      
    end
    
    ## This function set the positioning for the facets
    def generate_facets_metadata
      facets_result = @unbxd_response['facets']
      final_list_id_filters = []
      final_list_ranges = []
      position = 0
      if facets_result['text'].present?
        facets_result['text']['list'].each do |facet|
          final_list_id_filters << set_facet_fields_positioning(facet, position)
          position = position + 1
        end
      end
      
      if facets_result['range'].present?
        facets_result['range']['list'].each do |facet|
          final_list_ranges << set_facet_range_positioning(facet, position)
          position = position + 1
        end
      end
      final_result = {
        'range_filters': final_list_ranges,
        'id_filters': final_list_id_filters
      }
      return final_result
    end
    
    ## This function will handle API Response and generate metadata for design, facets and search.
    def handling_search_response_callback
      if @response.code.to_i == 200
        if @unbxd_response.has_key?('error')
          final_result = {'success' => 0, 'data' => @unbxd_response}
        else
          if @unbxd_response.has_key?('didYouMean')
            didYouMean = @unbxd_response['didYouMean'][0]['suggestion']
          end
          
          number_of_products = @unbxd_response['response']['numberOfProducts']
          final_result = {'success' => 1, 'unbxdparam_request_id'=> @response.header['Unx-Request-Id']}
          if number_of_products.eql?(0)
            # this is actually success 0 but had configured it to be 1 cos view was breaking for some reason #
            final_result = final_result.merge({'message' => 'either error or no results', 'num_products' => number_of_products, 'didYouMean' => didYouMean})
          else
            final_result = final_result.merge({'search' => generate_paginated_metadata(number_of_products)})
            final_result['search']['designs'] = generate_design_metadata('search')
            if @unbxd_response.has_key?('facets')
              final_result['search']['filters'] = generate_facets_metadata
            end
          end
        end
      else
        final_result = {'success' => 0, 'message' => 'status-code-unbxd:' + @response.code.to_s}
      end

      return final_result.with_indifferent_access
    end



    private
    def check_valid_params
      return false if @params.empty? || (@params.present? && @params['q'].blank?)
      return true
    end
    
    ## This function will fetch data from unbxd and pass control to handling_search_response_callback function
    def send_unbxd_search_request
      search_base_url = "https://search.unbxd.io/#{ENV['UNBXD_API_KEY']}/#{ENV['UNBXD_SITE_KEY']}/search?#{URI.encode_www_form(@payload)}"
      decoded_url = URI.unescape(search_base_url)
      @search_api_url = decoded_url.gsub('range_filters', 'filter')
      get_request(@search_api_url, "handling_search_response_callback")
    end
  end
end
