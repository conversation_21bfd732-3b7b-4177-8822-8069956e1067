class ReviewsController < ApplicationController
  before_filter :authenticate_account!, only: [:save_review]
  
  def site_review
    if request.post?
      response ={error: 'something went wrong'}
      if current_account.present? && params[:review].present? && params[:star].present?
        if (@review = Review.where(user_id: current_account.try(:accountable_id),site_review: true).first).present?
          @review.review = params[:review]
          @review.rating = params[:star]
          @review.approved = true
        else
          @review = Review.new(review: params[:review], rating: params[:star], site_review: true, user_id:current_account.try(:accountable_id))
        end
        @review.save
        response = {success: true}
      elsif params[:review_id].present?
        Review.where(id: params[:review_id]).first.update_column(:approved, false)
        response = {success: true}
      end
      render json:  response
    else
      @seo = SeoList.select('title, keyword, description, top_content, post').where(label: 'review_page').first
      @site_reviews = Review.preload(:user).site.approved.comments.order("(cast(extract(epoch from (now() + interval '7' day)) as float) - cast(extract(epoch from updated_at) as float)) + ((5 - rating) * 100) asc").page(params[:site_page]).per(params[:per] || 10)
      reviews = Review.where("rating > 1").preload(:design, :user, :designer, [design: :master_image]).product.approved.comments.with_enabled_designer_reviews
      @reviews = reviews.order("(cast(extract(epoch from (now() + interval '7' day)) as float) - cast(extract(epoch from updated_at) as float)) + ((5 - rating) * 100) asc").page(params[:page]).per(params[:per] || 10)
      @best_reviewed_designs = reviews.joins(:design).where('rating > 4 and designs.total_review > 20').order("(cast(extract(epoch from (now() + interval '7' day)) as float) - cast(extract(epoch from reviews.updated_at) as float)) + ((5 - rating) * 100) asc").page(params[:reviewed_best]).per(params[:per] || 10)
      if (user_id = current_account.try(:accountable_id)).present?
        @review = Review.site.where(user_id: user_id).first
      end
    end
  end
  
  def design_review
    if params[:design_id].present? && (design_id = Design.select('designs.id').find_by_cached_slug(params[:design_id]).try(:id)).present?
      geo = @country_code == 'IN' ? 'domestic' : 'international'
      reviews = Review.where(:design_id => design_id, :geo => geo).preload(:user,[design: :master_image]).product.approved.comments.with_enabled_designer_reviews
      @reviews = reviews.order("(cast(extract(epoch from (now() + interval '7' day)) as float) - cast(extract(epoch from updated_at) as float)) + ((5 - rating) * 100) asc").page(params[:page]).per(params[:per] || 10)
      @best_reviewed_designs = reviews.joins(:design).where('rating > 4 and designs.total_review > 20').order("(cast(extract(epoch from (now() + interval '7' day)) as float) - cast(extract(epoch from reviews.updated_at) as float)) + ((5 - rating) * 100) asc").page(params[:reviewed_best]).per(params[:per] || 10)
    else
      render :nothing => true
    end
  end

  # Create review of a particular design for a particular user or update it if already exists
  # == Input: 
  # POST
  #   Params[:rating], params[review], params[design_id]
  # == Returns:
  # JSON
  def save_review
    if (design = Design.find_by_id(params[:design_id])).present? && design.designer.enable_reviews
      begin
        review = current_account.user.reviews.where(design_id: design.id, designer_id: design.designer_id).first_or_initialize
        review.review = params[:review].to_s unless params[:review] == 'false'
        review.rating = params[:rating].to_i
        review.user_id = current_account.try(:accountable_id)
        review.save!
      rescue
        render json: { message: 'Cannot review this product', status: 422}
      end
      render json: { message: 'Review saved successfully', status: 200}
    else
      render json: { message: 'Unable to save a review', status: 422}
    end
  end
end
