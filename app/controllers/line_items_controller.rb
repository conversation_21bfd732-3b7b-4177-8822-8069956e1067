class LineItemsController < ApplicationController
  include Api::V1::DesignsHelper
  after_filter :set_dynamic_cookie
  after_filter :check_coupon_validity, only: [:destroy]
  # Creates LineItem
  #
  def create
    design_ids = (params[:line_items] || {}).map{|index,d| d['design_id']}
    designs = Design.where(:id => design_ids).preload(:variants,:categories).group_by(&:id)
    render nothing: true and return if designs.blank?
    multi_ga_hash = []
    ads_data = []
    multi_line_item_params["line_items"].each do |_,item_param|
      design = designs[item_param['design_id'].to_i].try(:first)
      addon_types = STITCHING_ENABLE == 'true' ? design.addons_association_not_for(region_is_not?) : []
      if design.variants.present?
        add_line_item = item_param[:variant_id].present?
      elsif addon_types.present?
        add_line_item = item_param[:line_item_addons_attributes].present?
      else
        add_line_item = true
      end
      if add_line_item
        design
        line_item = current_web_cart.line_items.where(item_param.slice(:design_id, :variant_id)).first_or_initialize
        line_item.add_variant(line_item.variant) if line_item.variant.present? 
        line_item.clear_add_addons(item_param)
        line_item.app_source = @app_source
        if item_param[:rakhi_note].present? && item_param[:rakhi_note] == 'true'
          line_item.add_note('Rakhi Schedule Delivery')
        end
        line_item.buy_get_free = design.buy_get_free if PromotionPipeLine.bmgnx_hash.present?
        line_item.pair_product = item_param[:pair_product]
        line_item.designable_type = design.designable_type
        line_item.category_id = design.categories.try(:first).try(:id)
        if (var_hash = Promotion.get_free_shipping(nil, true, country_code: @country_code)).last == true && line_item.snapshot_price > var_hash.first
          line_item.item_details['promotion'] = 'Free Shipping'
        end
        line_item.save
        current_pe_subscriber.product_added_to_cart if current_pe_subscriber
        ga_hash = line_item.ga_data
        # ga_hash[:gradingexperimentid] = session[:exp_category]['last'] if session[:exp_category].is_a?(Hash)
        multi_ga_hash.push(ga_hash)
        ads_data.push(line_item.google_ads_data)
      else
        render json: {url: designer_design_path(design.designer, design)} and return
      end
    end
    if @cart.present?
      cart_count = get_cart_count
      session[:cart_count] = cart_count
      country_code = @country_code
      if params[:design_page].present?
        render json: {redirect_url: cart_url, ga_hash: multi_ga_hash, cart_count: cart_count, country_code: country_code, ads_data: ads_data}
      else
        render json: {cart_count: cart_count, ga_hash: multi_ga_hash, country_code: country_code,ads_data: ads_data}
      end
    else
      render nothing: true
    end
  end

  # Updates LineItem
  #
  def update
    if line_item = current_web_cart.line_items.find { |li| li.id == params[:id].to_i }
      old_quantity = line_item.quantity
      line_item.update(line_item_params)
      session[:cart_count] = get_cart_count
      ga_hash = line_item.ga_data(old_quantity: old_quantity)
      country_code =  @country_code
    end
    ads_data = []
    if line_item.quantity> old_quantity
      ads_data.push(line_item.google_ads_data)
      ads_data[0][:contents][0][:quantity] = ads_data[0][:contents][0][:quantity] - old_quantity
    else  
      ads_data.push(line_item.google_ads_data)
      ads_data[0][:contents][0][:quantity] = old_quantity - ads_data[0][:contents][0][:quantity]
    end
    respond_to do |format|
      format.json {
        render json: {redirect_url: cart_url, ga_hash: ga_hash, country_code: country_code,ads_data: ads_data}
      }
    end
  end

  # Deletes LineItem
  #
  def destroy
    if line_item = current_web_cart.line_items.find { |li| li.id == params[:id].to_i }
      ga_hash = line_item.ga_data
      country_code =  @country_code
      current_web_cart.line_items.delete(line_item)
      line_item.save # Run Callbacks
      if params[:move_to_wishlist] && account_signed_in? && (user = current_user).present?
        wishlist = Wishlist.where(design_id: line_item.design_id, user: user).first_or_initialize
        wishlist.assign_attributes(conversion_rate: @rate, design: line_item.design,
          price: line_item.design.try(:effective_price),
          country_code: @country_code, wish: true, state: 'added', app_source: @app_source)
        wishlist.save
      end
      session[:cart_count] = get_cart_count
    end
    respond_to do |format|
      format.json {
        render json: {redirect_url: cart_url, ga_hash: ga_hash}
      }
      format.js {render inline: " 
      var ga4_remove_from_cart_params = #{ga_hash.to_json};
      ga4_remove_from_cart_params.price = ga4_remove_from_cart_params.price * ga4_remove_from_cart_params.quantity
      ga4_remove_from_cart_params.price = ga4_remove_from_cart_params.price + ga4_remove_from_cart_params.item_customization
      dataLayer.push({ ecommerce: null });
      dataLayer.push({
        event: 'ga4_remove_from_cart',
        ecommerce:{
          currency: 'INR',
          value: ga4_remove_from_cart_params.price,
          country_code: '#{@country_code}',
          items: [ga4_remove_from_cart_params]
        }
      });" }
      #location.reload(); add this piece of code in format.js whenever we need cart addons
    end
  end

  def check_coupon_validity
    if current_web_cart.coupon.present?
      unless current_web_cart.line_items.any? { |line_item| line_item.stitching_addons? }
        current_web_cart.coupon = nil
        current_web_cart.save
      end
    end
  end  
  private

  # Rails Strong parameters implementation
  # Blocking unwanted values
  #
  def multi_line_item_params
    params.permit(:design_page,line_items: [:design_id, :pair_product, :variant_id, :quantity, :rakhi_note, line_item_addons_attributes: [:addon_type_value_id, :notes]])
  end

  def line_item_params
    params.require(:line_items).permit(:design_id, :variant_id, :quantity)
  end

  # Rails Strong parameters implementation
  # Blocking unwanted values
  #
  def line_item_addons_params
    params.require(:line_items).permit(line_item_addons_attributes: [
      :addon_type_value_id, :notes, addon_option_type_id: []])
  end
end
