class Api::V1::AssetsController < ApiController

  caches_action :currency, expires_in: API_CACHE_LIFESPAN.minutes, cache_path: :api_v1_currency

  skip_before_filter :validate_headers?, :currency, except: [:env_vars]
  skip_after_action :update_auth_header
  before_filter :set_cart, if: :web_request?
  # Provides Menus and associated relationships objects
  #
  # == Returns:
  # JSON
  #
  def menu
    @app = @app_source.split('-')[0]
    sub_app = request.headers['Sub-App']
    @menu = Menu.menu_by_hide_appsource_country(@country_code, @app, sub_app)
    if @app == 'Android' && request.headers['App-Version']=='1.9.8'
      @country_code
    end
    expires_in 12.hours
    head :no_content if @menu.blank?
  end

  def horizontal_menu
    @app = @app_source.split('-')[0]
    @menu_tabs = Menu.menu_for_tags_country(@country_code, @app)
    expires_in 12.hours
    head :no_content  if @menu_tabs.blank?
  end

  # Provides active Frontpages objects
  #
  # == Returns:
  # JSON
  #
  def frontpage
    sub_app = @app_source.split('-')[1]
    @frontpages = sub_app ?
                  Frontpage.graded.live.with_link_value.where("mobile_menu= ? and (category= ? or category= ?)", true, sub_app, 'universal').country(@country_code).app_source_with_version(APP_SOURCE[1],request.headers['App-Version']) :
                  Frontpage.graded.live.with_link_value.where("mobile_menu= ? and (category= ? or category= ?)", true, '', 'universal').country(@country_code).app_source_with_version(@app_source,request.headers['App-Version'])
    expires_in 12.hours
    if STAGED_APP == "#{@app_source}-#{request.headers['App-Version']}"
      @frontpages = Frontpage.graded.live.where(app_source: 'staged_app')
    end
    head :no_content if @frontpages.blank?
  end

  # Provides Currencies objects
  #
  # == Returns:
  # JSON
  #
  def currency
    @currencies = CurrencyConvert.currency_convert_memcached
    head :no_content if @currencies.blank?
  end

  # Provides BannerSlider objects
  #
  # == Returns:
  # JSON
  #
  def banner_slider
    sub_app = @app_source.split('-')[1]
    @banner_sliders = sub_app ?
                      BannerSlider.graded.mirraw.live.with_link_value.where("app_source= ? or app_source= ?", sub_app, 'universal').country(@country_code) :
                      BannerSlider.graded.mirraw.live.with_link_value.country(@country_code).app_source_with_version(@app_source,request.headers['App-Version'])
    @tags = sub_app.present? ?
            TagSlider.graded.with_link_value.where(app_source: sub_app) :
            TagSlider.graded.with_link_value.app_source(@app_source)
    expires_in 6.hours
    if STAGED_APP == "#{@app_source}-#{request.headers['App-Version']}"
      @banner_sliders = BannerSlider.graded.mirraw.live.with_link_value.where(app_source: 'staged_app')
    end
    head :no_content if @banner_sliders.blank? && @tags.blank?
  end

  def story_collections_set
    story_board = Board.select(:id).find_by_id(params[:widget_id])
    head :no_content and return if story_board.blank?

    @story_collections = story_board.story_collections.preload(designs: [:designer, :master_image, :variants, :categories, :dynamic_prices]).live.graded
    @story_collections = @story_collections.mirraw.swiped_story_collection(visited_collections: params[:visited_sc]) if params[:visited_sc].present?
  end
  # Provides BannerSlider and Frontpages objects
  #
  # == Returns:
  # JSON
  #
  def frontpage_with_banners
    @frontpages = Frontpage.graded.live.with_link_value.where("mobile_menu= ? and (category= ? or category= ?)", true, '', 'universal').country(@country_code).app_source_with_version(APP_SOURCE[1],request.headers['App-Version'])
    @banner_sliders = BannerSlider.graded.mirraw.live.with_link_value.un_apped.country(@country_code).app_source(APP_SOURCE[1])
    expires_in 6.hours
    head :no_content if @banner_sliders.blank? && @frontpages.blank?
  end

  def frontpage_with_tags
    sub_app = @app_source.split('-')[1]
    @frontpages = sub_app ?
                  Frontpage.graded.live.with_link_value.where("mobile_menu= ? and (category= ? or category= ?)", true, sub_app, 'universal').country(@country_code).app_source_with_version(APP_SOURCE[1],request.headers['App-Version']) :
                  Frontpage.graded.live.with_link_value.where("mobile_menu= ? and (category= ? or category= ?)", true, '', 'universal').country(@country_code).app_source_with_version(@app_source,request.headers['App-Version'])
    @banner_sliders = sub_app ?
                      BannerSlider.graded.mirraw.live.with_link_value.where("app_source= ? or app_source= ?", sub_app, 'universal').country(@country_code) :
                      BannerSlider.graded.mirraw.live.with_link_value.country(@country_code).app_source_with_version(@app_source,request.headers['App-Version'])
    @tags = sub_app ?
            TagSlider.graded.with_link_value.where(app_source: sub_app) :
            TagSlider.graded.with_link_value.app_source(@app_source)
    expires_in 2.hours
    head :no_content if @banner_sliders.blank? && @frontpages.blank? && @tags.blank?
  end

  # Provides Feature Product objects
  #
  # == Returns:
  # JSON
  #
  def feature_products
    @designs = Rails.env.development? ? Design.limit(16) :
      Design.where('created_at > ?', 15.days.ago).order('sell_count DESC').limit(16)
    @designs = @designs.includes(:master_image, :designer)
    expires_in 2.hours
    head :no_content if @designs.blank?
  end

  # Provides all the environment controlling values controlled from server
  #
  # == Returns:
  # JSON
  #
  def env_vars
    @referrals_active = referrals_active?(request.headers['App-Version'])
    @referrals_amt_valid = referrals_valid?(request.headers['App-Version'])
  end

  def return_reason
    render json: { reasons: Return.get_return_reasons }
  end

  def frontpage_with_category
    @frontpages = Frontpage.graded.live.with_link_value.where("mobile_menu= ? and (category= ? or category= ?)", true, request.headers['Sub-App'], 'universal').country(@country_code).app_source(APP_SOURCE[1])
    head :no_content if @frontpages.blank?
  end

  def landing
    landing = Landing.mirraw.where(label: params[:landing_page]).first
    @blocks = landing.blocks.mirraw if landing.present?
    head :no_content if landing.blank? || @blocks.blank?
  end

  def reward
    if REFERRALS_ACTIVE == "true"
      @reward = CurrencyConvert.currency_convert_cache_by_country_code(@country_code)
    end
    head :no_content if @reward.nil?
  end

  def design_feed
    @designs = Kaminari.paginate_array(Design.get_feed_designs(@app_source, request.headers['App-Version'])).page(@page = params[:page]).per(params[:per] || 20)
    head :no_content if @designs.blank?
    #@wishlist_designs = Wishlist.get_ids_if_logged_in(current_user, :wish)
  end

  def hot_or_not
    if params[:search] == 'true'
      @designs = get_response_data_for('search', params)['search']
    else
      @designs = Kaminari.paginate_array(Design.get_feed_designs(@app_source, request.headers['App-Version'])).page(@page = params[:page]).per(params[:per] || 20)
    end
    head :no_content if @designs.blank?
    @like_designs = Wishlist.get_ids_if_logged_in(current_user, :like, true)
  end

  def design_policies
    @show = false
    @category = "default"
    if params[:design_id]
      if Design.is_luxury_category?(params[:design_id])
        @show = true 
        @category = "luxury"
      end
    end
  end

  def offers
    @designer_discounts = Designer.active_additional_discount.order("additional_discount_percent DESC").page(@page = params[:page]).per(params[:per] || 20)
    head :no_content if @designer_discounts.blank?
  end

  def child_category_tags
    @child_categories = Category.find_by_id(params[:category_parent_id]).try{|category| category.descendants}
    head :no_content if @child_categories.blank?
  end

  def bestseller_designs
    @bestseller_designs = Bestseller.get_bestsellers(@country_code)
    head :no_content if @bestseller_designs.blank?
    #@wishlist_designs = Wishlist.get_ids_if_logged_in(current_user, :wish)
  end

  def dynamic_homepage
    all_boards = Board.joins(:tabs).where(:tabs => {name: 'Default'}).preload(:frontpages, :banner_sliders, :tag_sliders, :designs)
    boards = all_boards.live.graded
    if STAGED_APP == "#{@app_source}-#{request.headers['App-Version']}"
      @staged_app = true
      boards = boards.where(app_source: 'staged_app')
    else
      boards = boards.country(@country_code).app_source(@app_source.split('-')[1] || @app_source)
    end
    #@max_update_at_cache_key = Board.get_cache_key(all_boards)
    items_per_page = @app_source == 'mobile' ? 10 : 15
    @boards = boards.where('board_type <> ?', "story_collection").page(params[:page]).per(items_per_page)
    @story_board =  (params[:page].to_i == 1 && @app_source.downcase.include?('android')) ? boards.where(board_type: "story_collection") : []
    @can_cache_full_response = !STORY_COLLECTION_CONSTANT['Story_queue_shuffle'] || @app_source == 'mobile' || @app_source.downcase.include?('ios')
    @seo = SeoList.where(label: 'home_page').first
    head :no_content unless @boards.exists?
  end

  def flash_deals
    if Promotion.flash_deals_on?(@country_code)
      @active_promotions ||= PromotionPipeLine.active_promotions
      @ongoing_fd, @upcoming_fd = RequestStore.cache_fetch("flash_deals_designs", expires_in: 12.hour) do
        ongoing_fd = DesignPromotionPipeLine.includes(design: [:designer, :master_image]).active_flash_deals.group_by{|d| d.end_date}.first(1).to_h || {}
        upcoming_fd = DesignPromotionPipeLine.includes(design: [:designer, :master_image]).upcoming_flash_designs.group_by{|d| d.end_date}.first(2).to_h || {}
        [ongoing_fd, upcoming_fd]
      end
    else
      head :no_content
    end
  end

  def tabs
    @tabs =  if params[:offers] == 'true'
      @type = 'offers'
      Tab.live.graded.active_tabs.offer_tabs
    else
      @type = 'default'
      Tab.live.graded.active_tabs.homepage_tabs
    end

    if STAGED_APP == "#{@app_source}-#{request.headers['App-Version']}"
      @staged_app = true
      @tabs = @tabs.where(app_source: 'staged_app')
    else
      @tabs = @tabs.country(@country_code).app_source(@app_source.split('-')[1] || @app_source)
    end
  end

  def newly_added_products
    if params[:category_id].present?
      category_ids = params[:category_id].split(',')
      cache = "category_#{params[:category_id]}"
    else
      category_ids = NEWLY_ADDED_PRODUCTS_CATEGORY
      cache = "cache"
    end
    if (newly_added_products_cache = Redis.current.get("newly_added_products_#{cache}")) && JSON.parse(newly_added_products_cache)
      render json: {from_cache: true, data: JSON.parse(newly_added_products_cache)}
    else
      in_catalog_one_value = Design.country_code == 'IN' ? 1 : 2
      preload_arr = [:designer, :master_image, :categories, :variants]
      preload_arr.push(:dynamic_price_for_current_country) if DYNAMIC_PRICE_ENABLED && Design.country_code != 'IN'
      products_of_each_category = (30.0/category_ids.length).ceil
      design_hash, designs_already_pluck  = [], []
      category_ids.each do |cid|
        designs_obj = Design.joins(:categories).where.not(id: designs_already_pluck).where(designer_id: NEWLY_ADDED_PRODUCTS_DESIGNER, created_at: 30.days.ago..Time.now, state: 'in_stock', in_catalog_one: [in_catalog_one_value,nil]).where(categories: {id: cid}).order('designs.created_at desc').preload(preload_arr).uniq.limit(products_of_each_category)

        designs_obj.each do |design|
          designs_already_pluck << design.id
          category = design.categories.first
          design_hash << {id: design.id, title: design.title, average_rating: design.average_rating, mirraw_recommended: design.mirraw_recommended?, category_name: category.try(:name).try(:singularize), category_id: category.try(:id), design_path: designer_design_path(design.designer, design), symbol: @symbol, string_symbol: @currency_symbol,price: design.price_currency(@rate), inr_price: Design.inr_value(design.price, @country_code, @rate), discount_price: design.effective_price_currency(@rate), inr_discount_price: Design.inr_value(design.effective_price, @country_code, @rate), discount_percent: design.effective_discount(PromotionPipeLine.active_promotions), master_image: design.master_image.photo('small')}
        end
        break if design_hash.length>=30
      end
      Redis.current.setex("newly_added_products_#{cache}", 3.hours.to_i, design_hash.to_json)
      render json: {from_cache: false, data: design_hash}
    end
  end

  def cart_coupons
    if @country_code == 'IN'
      coupons_hash = @coupons = Coupon.joins(:designer).select('coupons.id, coupons.code, coupons.percent_off, coupons.flat_off, coupons.min_amount, coupons.designer_id, coupons.coupon_type, coupons.end_date, designers.name AS designer_name, designers.photo_file_name AS designer_photo').live.designer_coupons.advertise.geo_domestic.by_designer.order('end_date asc').limit(30)
    else
      coupons_hash = @coupons = Coupon.joins('LEFT JOIN designers on designers.id = coupons.designer_id').select('coupons.id, coupons.code, coupons.percent_off, coupons.flat_off, coupons.min_amount, coupons.designer_id, coupons.coupon_type, coupons.end_date, designers.name AS designer_name, designers.photo_file_name AS designer_photo').live.advertise.geo_international.by_mirraw.order('end_date asc').limit(30)
    end
  end

  def active_coupons
    coupon_type = @country_code.present? && @country_code.downcase.eql?("in") ? "Domestic" : "International"  
    source_android = @app_source.downcase.include?('android') || @app_source.downcase.include?('all')
    source_ios = @app_source.downcase.include?('ios') || @app_source.downcase.include?('all')
    @coupons = if source_android
                Coupon.where(geo: [coupon_type, "All"]).live.mirraw_coupons.by_source('android')
               elsif source_ios
                 Coupon.where(geo: [coupon_type, "All"]).live.mirraw_coupons.by_source('ios')
               end
  end

  private

  def referrals_active?(app_version)
    REFERRALS_ACTIVE == "true" && REFERRALS_ACTIVE_COUNTRIES.include?(@country_code) && ALLOWED_APP_VERSIONS[10..-1].include?(app_version)
  end

  def referrals_valid?(app_version)
    app_versions = ALLOWED_APP_VERSIONS[10..-1] | ALLOWED_IOS_APP_VERSIONS[5..-1]
    @referrals_active || (REFERRALS_ACTIVE_COUNTRIES.include?(@country_code) && app_versions.include?(app_version) && Time.current < Date.parse(REFERRALS_EXPIRY))
  end

end
