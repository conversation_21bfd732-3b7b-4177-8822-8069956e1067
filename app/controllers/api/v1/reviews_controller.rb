class Api::V1::ReviewsController < ApiController

  before_action :authenticate_api_account!, :authenticate_user!, only: [:create]
  skip_before_filter :validate_headers?, :currency, only: [:rating, :index]
  skip_after_action :update_auth_header, only: [:rating, :index]

  # Provides rating for the design whose id is passed in params
  #
  # == Returns:
  # JSON
  # => API: GET api/v1/designs/design_id/reviews/rating
  #
  def rating
    @design = Design.find_by_id(params[:design_id])
    @curr_user = current_user
    head(:no_content) and return unless (@design)
    @rating, @reviews = Review.design_ratings(params[:design_id], current_user.try(:id))
    @reviews = @reviews.page(1).per(params[:per] || 10)
    @user_review = current_user.review_for(@design.id) if current_user.present?
  end

  # Provides reviews for the design whose id is passed in params
  #
  # == Returns:
  # JSON
  # => API: GET api/v1/designs/19/reviews?page=2
  #
  def index
    head :no_content and return unless Design.exists?(params[:design_id])
    @reviews = Review.approved.with_enabled_designer_reviews.where(design_id: params[:design_id]).where.not(review: nil, user_id: nil).order('updated_at DESC').page(params[:page]).per(params[:per] || 10)
  end

  # Create review of a particular design for a particular user or update it if already exists
  #
  # == Returns:
  # JSON
  # => API: POST api/v1/user/reviews?review[rating]=2&review[design_id]=design_id&review[review]=ok
  #
  def create
    design = Design.find_by_id(params[:review][:design_id])
    unless design.nil? || !design.designer.enable_reviews
      designer_id = design.designer_id
      review = current_user.reviews.where(design_id: params[:review][:design_id]).first_or_initialize
      review.attributes = review_params.merge(designer_id: designer_id)
      review.save
    end
    if review.nil? || review.errors.any?
      error_message = review&.errors&.messages[:base]&.first || 'Reviews are not enabled for this designer'
      render json: { errors: error_message }, status: 422 and return
    else
      response_for review
    end
  end

  def notified_reviews
    review_ids = params[:review_ids].split(',')
    @reviews = Review.where(id: review_ids).with_enabled_designer_reviews.includes(:design, [design: :designer], [design: :master_image], :user).order('updated_at DESC').group_by(&:user_id)
    @reviews = Kaminari.paginate_array(@reviews.to_a).page(params[:page]).per(params[:per] || 20)
  end

  private
  # Permitting only allowed params for creating or updating review
  #
  def review_params
    params.require(:review).permit(:rating, :design_id, :review, :default)
  end
end
