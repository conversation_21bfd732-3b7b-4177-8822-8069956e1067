class Api::V1::SurveysController < ApiController
  include SurveysHelper

  before_action :validate_params, except: [:design_review]

  def questions
    head :unauthorized and return unless Survey.survey_valid?(survey_get_params)
    @questionnaire_hash = {}
    if (@order = Order.includes(line_items: [design: :designer]).find_by_number(survey_get_params[:order_number])).present?
      unless @order.feedback_flag || Survey.exists?(email: survey_get_params[:user_email], order_id: @order.id)
        @questionnaire_hash = SurveyQuestion.get_questionnaire
      end
      @stitching_items = check_stitching_items(@order.id)
    else
      head :bad_request and return
    end
  end

  def create
    head :unauthorized and return unless Survey.survey_valid?(survey_post_params)
    if (@order = Order.find_by_number(survey_post_params[:order_number])).present? && @order.feedback_flag.blank?
      Survey.create_by(survey_post_params, @order, @app_source)
      SurveyAnswer.fill_answers(params[:answers], 'order', @order.id) if params[:answers].present?
      head :ok and return
    else
      head :bad_request and return
    end
    rescue
      render json: {errors: 'Something went wrong, please try again later!'}, status: 422 and return
  end

  def design_review
    order = Order.find_by_id(params[:order_id])
    design = Design.find_by_id(params[:design_id])
    if order.present? && design.present? && design.designer.enable_reviews
      review = Review.where(user_id: order.user_id, design_id: review_params[:design_id]).first_or_initialize
      review.update_attributes!(
        review_params.merge(
          user_id: order.user_id,
          designer_id: design.designer_id,
          geo: order.geo
        )
      )
      review.orders << order unless review.orders.include?(order)
      SurveyAnswer.fill_answers(params[:answers], 'review', review.id) if params[:answers].present?
      head :ok and return
    else
      head :bad_request and return
    end
    rescue Exception => ex
      render json: {errors: "An error of type #{ex.class} happened, message is #{ex.message}"}, status: 422 and return
  end

  private

  def survey_post_params
    params.permit(:user_email, :order_number, :token, :rating)
  end

  def survey_get_params
    params.permit(:user_email, :order_number, :token)
  end

  def review_params
    params.permit(:order_id, :design_id, :rating, :review, :issue)
  end

  def validate_params
    method = request.method
    head :bad_request and return if (method == 'GET' && survey_get_params.count != 3) || (method == 'POST' && survey_post_params.count != 4)
  end

end