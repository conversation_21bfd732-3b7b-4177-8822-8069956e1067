class Api::V1::StoreController < ApiController
  include Api::V1::StoreHelper

  after_action :update_response, only: [:search, :search2]
  before_action :check_for_exceptions
  before_action :handle_web_request, only: [:search2, :filters], if: :web_request?
  before_action :validate_search_query, only: [:search2, :filters, :search]
  

  skip_before_filter :set_paper_trail_whodunnit, only: [:search, :search2]
  skip_before_filter :set_api_data, only: [:search]

  # no need for 'kind'
  SEARCH_PARAMS = [
    'category_child_ids', 'category_parent_id', 'collection','catalogue',
    'designer_ids', 'designer_id', 'in_catalog_one', 'items_per_page', 'max_discount',
    'max_price', 'min_discount', 'min_price', 'option_type_value_ids', 'page',
    'property_value_ids', 'colour_property_value_ids', 'sort', 'state', 'tag',
    'term', 'Currency-Code', 'category_name', 'designer_name', 'created_at', 'Country-Code',
    'min_rating', 'timer', 'buy_get_free', 'pincode', 'premium','category_grade', 'max_odr', 'preference', 'gender',
    'app_source', 'order_seq', 'grade_name'
  ].freeze

  SORT = {
        'top_rated' => '1',
        'l2h' => '2',
        'h2l' => '3',
        'new' => '4',
        'discount' => '5',
        'popularity' => '6',
        'bstslr' => '6',
        'default' => '7',
        'trending' => '8',
        'trending-designs'=> '9',
        'popular' => '10', #for experiment
        'recommended' => '11', #for experiment
        'recent-30' =>  '14'
      }.freeze

  caches_action :search, cache_path: proc { |c|
    c.params['Currency-Code'] = @symbol
    c.params['Country-Code'] = Design.country_code
    c.params[:page] = 1 if c.params[:page].to_i <= 0
    c.params[:app_source] = MirrawUtil.platform if Design.app_specific_grading? && params[:sort].blank?
    modified_params = c.params.reject { |key, value| SEARCH_PARAMS.exclude? key }
    "api/v1/store/search?#{modified_params.sort.to_h.to_query}"
  }, expires_in: API_CACHE_LIFESPAN.minutes

  caches_action :search2, cache_path: proc { |c|
    c.params['Currency-Code'] = @symbol
    c.params['Country-Code'] = Design.country_code
    c.params[:page] = 1 if c.params[:page].to_i <= 0
    c.params[:app_source] = MirrawUtil.platform if Design.app_specific_grading? && params[:sort].blank?
    modified_params = c.params.reject { |key, value| SEARCH_PARAMS.exclude? key }
    "api/v1/store/search2?#{modified_params.sort.to_h.to_query}"
  }, expires_in: API_CACHE_LIFESPAN.minutes

  caches_action :filters, cache_path: proc { |c|
    c.params['Country-Code'] = Design.country_code
    c.params.delete_if { |key, value| SEARCH_PARAMS.exclude? key }
  }, expires_in: API_CACHE_LIFESPAN.minutes

  # Provides search results with facets based on params
  #
  # == Parameters:
  # opt::
  #   Expects a hash containing values - category_child_ids,
  #   category_parent_id, collection, designer_ids, in_catalog_one,
  #   items_per_page, max_discount, max_price, min_discount, min_price,
  #   option_type_value_ids, page, property_value_ids, sort, state, tag, term
  #
  # == Returns:
  # JSON
  #
  def validate_search_query
    # Pattern to check for Log4j-style injections
    log4j_pattern = /\$\{.*?\}/
    # Pattern to check for any invalid characters (anything other than alphanumeric, hyphen, or space)

    if (((term = params[:q]).present? || (term = params[:term]).present?) && (term =~ log4j_pattern))
      Order.sidekiq_delay.notify_exceptions("Suspicious search query detected API!", "Some One is Trying To Inject Malicious Query", term)
      render json: {status: 404}
    end
  end
  
  def search
    if params[:kind] == 'direct_dollar'
      params[:buy_get_free] = 4
    end
    # head :no_content and return if params['buy_get_free'] && Design.country_code == 'IN'
    set_results
    head :no_content if @results.raw_results.blank?
  end

  def search2
    # head :no_content and return if params['buy_get_free'] && Design.country_code == 'IN'
    set_results
    head :no_content if @results.raw_results.blank?
  end

  def filters
    set_results
  end

  def facet_url
    facet_properties = []
    link = ''
    if FACETED_URL_KINDS[params[:kind]].present?
      if params[:property_value_ids].present?
        pv_ids = params[:property_value_ids].split(',')
        pv_ids.each do |pv_id|
          pv = PropertyValue.find_by_id(pv_id)
          type = pv.property.name.match(/color/i) ? 'checkbox' : 'radio'
          priority = pv.property.facet_priority
          if priority > 0 && create_url_for_facets?(type, facet_properties)
            new_property = {name: pv.name, priority: priority, type: type, id: pv.id}
            link = create_faceted_url(params[:kind], new_property, facet_properties, '')
            facet_properties << new_property
          end
        end
      end
    end
    @url_params = create_url_parameters(facet_properties, params)
    @facet = link.to_s
  end

  private

  def get_kind_category_id(kind)
    kind = kind.downcase
    if params[:facets].present?
      if params[:facets].try(:index, 'colour-') == 0
        @colour_facets = params[:facets].partition('colour-').last.split('--')
        seo_labels = @colour_facets.map { |color| kind + '-' + color }
        seo = SeoList.where(:label => seo_labels).to_a
        @seo = seo.first
        @seo ||= SeoList.where(:label => kind).includes(:category).first
      else
        seo_labels = params[:facets].gsub('_', '-')
        seo = SeoList.where(label: seo_labels).to_a
        @seo = seo.first
      end
      seo.to_a.each do |s|
        @seo.keyword += ', ' + s.keyword.to_s if @seo.keyword != s.keyword
      end
    else
      @seo = SeoList.where(:label => kind).includes(:category).first
    end
    if @seo && @seo.category.present?
      category = @seo.category
    elsif (category = Category.find_by_namei(kind)).present?
      @seo ||= SeoList.find_by(category_id: category.id)
    end
    category
  end

  def set_results
    pre_process(params)
    @results = solr_results(params)
    @debug = params
    post_process(params) unless @results.raw_results.blank?
  end

  def pre_process(params)
    if web_request?
      params.merge!(params) do |key, key_new, value|
        if(value.is_a?(Array))
          value.join(', ')
        else
          value
        end
      end
    elsif params[:collection].present?
      params.merge!(Rack::Utils.parse_nested_query("collection=#{params[:collection]}"))
    elsif params[:catalogue].present?
      params.merge!(Rack::Utils.parse_nested_query("catalogue=#{params[:catalogue]}"))
    end
    if params[:gender] && !web_request?
      params[:category_parent_id] = Category.find_by_namei("kids-#{params[:gender]}").try(:id)
    end
    if params[:category_parent_id].to_i > 0 && (@category = Category.find_by_id(params[:category_parent_id])) && @category.searchable?
      params[:category_parent_id] = nil
      params[:term] = @category.p_name
    end
    set_grade_rank_tag(params)
    params[:order_seq] = 'desc'
    params[:copyright_filter] = true if %w(Android iOS).include?(request.headers['App-Source'])
    params.sort.to_h
  end

  def set_grade_rank_tag(params)
    app_source = @app_source || request.headers['App-Source']
    catalogue_params = params[:catalogue].downcase.gsub(/[^a-z0-9]/, '') if params[:catalogue].present?
    collection_params = params[:collection].downcase.gsub(/[^a-z0-9]/, '') if params[:collection].present?
    if app_source.present? && @country_code.present? && params[:sort].blank?
      if (category_id = params[:category_parent_id]).present? || (category_id = @category&.id).present? 
        params[:grade_name] = GradingTag.get_grading_tags_for_grade_name(category_id,'Category', @country_code, app_source)
      elsif params[:collection].present? && (collection = ActsAsTaggableOn::Tag.find_by("LOWER(REGEXP_REPLACE(name, '[^a-z0-9]', '', 'g')) = ?",collection_params)).present?
        params[:grade_name] = GradingTag.get_grading_tags_for_grade_name(collection.id,'Collection', @country_code, app_source)
      elsif params[:catalogue].present? && (catalogue = ActsAsTaggableOn::Tag.find_by("LOWER(REGEXP_REPLACE(name, '[^a-z0-9]', '', 'g')) = ?",catalogue_params)).present?
        params[:grade_name] = GradingTag.get_grading_tags_for_grade_name(catalogue.id,'Catalogue', @country_code, app_source)
      end  
    end
  end

  def post_process(params)
    facet_property_value_ids = @results.facet(:property_value_ids)
    facet_property_value_union = @results.facet(:property_value_ids_union)
    if params[:property_value_ids].present? && facet_property_value_ids.present? && facet_property_value_union.present?
      union_hash = {}
      facet_property_value_union.rows.each do |row|
        union_hash[row.value] = row.count
      end
      property_ids = PropertyValue.where(id: params[:property_value_ids]).uniq.pluck(:property_id)
      pv_ids = PropertyValue.where{id.in(union_hash.keys)&property_id.in(property_ids)&id.not_in(params[:property_value_ids])}.pluck(:id)
      pv_ids = union_hash.slice(*pv_ids).sort_by(&:last).collect(&:first).reverse!
      pv_ids.each do |pv_id|
        count = if RANDOM_FILTER_COUNT_ACTIVATED
          union_hash[pv_id] > 100 ? 100 + rand((union_hash[pv_id]-100)/10).to_i : union_hash[pv_id]
        else
          0
        end
        facet_property_value_ids.rows << Sunspot::Search::FacetRow.new(pv_id,count,facet_property_value_ids)
      end
    end
  end

  def update_response
    return if response.body.blank?
    resp = JSON.parse(response.body, :quirks_mode => true)
    ###### Added the below loop  for manyavar pricing adjustment ######
    for design in resp['search']['designs']
      if( design["designer"] == "Manyavar") 
       design["inr_price"]        = design["inr_discount_price"] 
       design["price"]            = design["discount_price"]
       design["discount_percent"] = 0 
      end
    end
    
    if params[:pid].present? && [0, 1].include?(params[:page].to_i)
      pids = params[:pid].split(',').sort!.join(',')
      adverb_designs = get_response_data_for('preview_api_v1_designs', {request_url_provided: true, ids: pids})['designs']
      resp['search']['designs'].try{|designs| designs.unshift(*adverb_designs).pop(adverb_designs.length)} if adverb_designs.present?
    end
    wishlist_designs = Wishlist.get_ids_if_logged_in(current_user)
    resp['search']['designs'].each{ |design| design['wishlist_id'] = wishlist_designs[design['id']].try(:first).try(:id) }
    # if MOBILE_SOURCE && ENV['DW_TAGS_ROLLOUT'].to_i > 0
    #   uuid = resp['search']['uuid']
    #   Redis.current.hmset('dw_tags', uuid, resp.to_json) if uuid.present? && rand(1..ENV['DW_TAGS_ROLLOUT'].to_i) == 1
    # end
    response.body = resp.to_json
  end

  def check_for_exceptions
    sub_app = request.headers['Sub-App']
    app_source = request.headers['App-Source']
    if sub_app.present?
      params['premium'] = true if sub_app.include?('Premium')
      params['designer_id'] = sub_app.split('_')[-1].to_i if sub_app.include?('Designer') && params['designer_id'].blank?
    end
    if app_source.present? && (app_source == 'iOS' || app_source == 'Android') && (default_rating = DEFAULT_MIN_RATING.to_i) > 0
      params['min_rating'] = [params['min_rating'].to_i, default_rating].max
    end
  end

  def handle_web_request
    if params[:kind].present? || params[:category_parent_id].present?
      if params[:kind] == 'b1g1'
        params[:buy_get_free] = 1
      else
        params[:kind] = "kids-#{params[:gender]}" if params[:gender].present?
        @category = get_kind_category_id(params[:kind]) if params[:kind].present?
        params[:category_parent_id] ||= @category.try(:id)
        @category_parent_id = params[:category_parent_id]
        @category ||= Category.find_by_id(@category_parent_id)
        @popular_search = @category.popular_links if @category.present?
        @seo ||= SeoList.where(label: @category.try(:name).try(:downcase)).first
        @category_links = CategoryNewArrival.get_links(@category_parent_id, @country_code)
        @category_menu_links = CategoryNewArrival.get_links_for_nav_bar(@category_parent_id, @country_code)
        @breadcrumbs = generate_breadcrumb(category: @category, facet_name: params[:facets])
        show_details = FACETED_URL_KINDS[params[:kind]].present?
        imp_pvids = params[:facets].present?  ? PropertyValue.get_property_values_for_facets(params[:kind], params[:facets], details: show_details) : [] 
        if show_details
          imp_pvids = imp_pvids.collect { |prop| prop[:id] }
        end
        pv_id = imp_pvids.empty? ? '' : imp_pvids.join(',')
        params[:colour_property_value_ids] = pv_id if params[:facets] && params[:facets].index('colour-')
        if pv_id.present?
          params[:property_value_ids] = [params[:property_value_ids], pv_id].compact.join(',')
        end
      end
      @faqs =
        if params[:facets].present?
          FAQ.joins(:property_values).where({
            'property_values.id': imp_pvids
          })
        else
          FAQ.joins(:categories).where({
            'categories.id': params[:category_parent_id]
          })
        end.order(updated_at: :desc).limit(5).map do |faq|
            faq.attributes.slice('id', 'question', 'answer')
        end.reverse
      if CATEGORY_MOBILE_GRADE.include? params[:kind]
        params[:category_grade] = params[:kind]
      end
    elsif params[:q].present? && params[:utf8].blank?
      params[:tag] = params[:q]
      @seo = SeoList.where(:label => params[:tag]).first
    elsif params[:q].present?
      params[:term] = params[:q]
    elsif params[:collection].present?
      @seo = SeoList.where(:label => "#{params[:collection]}_collection").first
      @collection_heading = @seo&.heading.presence || params[:collection]
    elsif params[:catalogue].present?
      @seo = SeoList.where(:label => "#{params[:catalogue]}_catalogue").first
      @catalogue_heading = @seo&.heading.presence || params[:catalogue]
    elsif (designer_id = params[:designer_id]).present? || (designer_id = params[:id]).present?
      @designer = Designer.find_by_cached_slug(designer_id)
      @seo = SeoList.where(:label => designer_id).first
      params[:designer_id] = @designer.try(:id) unless params[:designer_id].present?
      @designer_search = true
      @breadcrumbs = generate_breadcrumb(designer: @designer)
    end
    set_seo
  end
end