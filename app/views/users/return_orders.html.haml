= stylesheet_link_tag 'returns'
.user_return_panel.return-block
  -if @active_returns.present?
    -@active_returns.each do |returns|
      -next unless returns.line_items.present?
      -return_order = returns.order
      -row_style    = '#010710'
      -text_color   = 'white'
      -return_track = false
      -if (returns.new? && returns.app_source.present?) || returns.images_approved?
        -returns.return_designer_orders.each do |rdo|
          -if (rdo.tracking_company.blank? || rdo.tracking_number.blank? || returns.wrong_track_details?)
            -row_style = '#F2DEDE'
            -text_color= 'black'
            -return_track=true

      .row.return_block
        .columns
          %h5
            -case returns.state
            -when 'pending_payment'
              .label.success='Approved'
            -when 'wrong_track_details'
              .label.warning='Rejected'
            -when 'payment_complete'
              .label.info='Completed'
            -when 'canceled'
              .label.warning='Canceled'
            -else
              .label.alert='Pending'
          Thank you for your return request. Our team will get back to you in 2 - 3 Business days
          %br
          %br
          %span Order Number :
          =return_order.number
          %br
          .return_desc
            %span Created on :
            =returns.created_at.strftime('%A,%B %d, %Y')
            %br
            %span Total Refund : 
            ="#{return_order.currency_code} #{get_price_in_currency(returns.discount,return_order.currency_rate)}"
            -if returns.type_of_refund.present?
              %br
              %span Return Type :
              = returns.type_of_refund
        .columns
          %hr
          -if return_track
            -if returns.reverse_pickup?
              = link_to 'View Return Details',user_order_return_items_path(order_number: return_order.number,return_id: returns.id), {method: :get, class: 'button radius tiny'}
            -else
              - if Return::RETURN_REASON_ARRAY.exclude?(returns.reason)
                = link_to 'Fill Tracking Details',user_order_return_items_path(order_number: return_order.number,return_id: returns.id), {method: :get, class: 'button radius tiny'}
          -else
            = link_to 'Track Return', user_order_return_items_path(order_number: return_order.number,return_id: returns.id), class: 'button radius tiny'
    .row
      .pagination_orders_returns= paginate @active_returns, :inner_window => 2, :previous_label => 'Previous', :next_label => 'Next', :outer_window => 0
  -else
    .no-order-heading
      %h3 No returns found
      %p Browse through our fabulous Ethnic products. Checkout our gorgeous products from exclusive designers. Shop now !
      #action_buttons.empty-cart-action-buttons
        = link_to 'CONTINUE SHOPPING',"/store/women" , class: 'add_cont_shop no-order-found'