- if @seo.present?
  - title_key_word = params[:page] || params[:reviewed_best] || params[:site_page]
  - if title_key_word
    - title  "Page #{title_key_word} of #{@seo.title.to_s}"
    - keywords 'mirraw reviews'
    - description  "Searching for solution of your problems? Drop us your feedback here or Call us on #{MIRRAW_CONTACT_INFO} now to resolve your queries."
  -else
    - title  @seo.title.to_s
    - description @seo.description.to_s
    - keywords  @seo.keyword.to_s

= stylesheet_link_tag "reviews"
-color_classes = ['#FF5252', '#64B5F6', '#81C784', '#FF8A65', '#90A4AE', '#AED581', '#4DD0E1', '#9575CD', '#8D6E63', '#757575']
.review-container
  %h1.heading
    = @seo.try(:post) || "Mirraw Reviews"
  .heading-content
  #add_review_button.tabbed-btn
    .add-new-review
      -if current_account.present?
        %button#add_review.btn.btn-custom-blue.btn-md{"data-reveal-id" => "write_review", href: "#"}
          -if @review.present?
            + Edit Review
          -else
            + Write a Review
      -else
        %a{href: new_account_session_url(protocol: Rails.application.config.partial_protocol), style:"color:white !important;"}
          %button#add_review.btn.btn-custom-blue.btn-md
            + Write a Review
      #write_review.reveal-modal.black-content{"aria-hidden" => "true", "aria-labelledby" => "modalTitle", "data-reveal" => "", role: "dialog"}
        %h4#modalTitle
          Share your review
        .ratings-score
          #form-star.star{"aria-expanded": "false", "aria-haspopup": "true", type: "button", style:"background:none !important;","data-star":2.5,dscore: 2.5}
        %textarea#review-text.black{cols: 75, rows: 10,placeholder: 'Write a Review',style:'padding:20px;margin-top:10px;'}
        %button#submit-review-btn.btn.btn-custom-green{"data-dismiss": "modal", type: "button", style: "margin-top:15px !important;", "data-email": "#{current_account.try(:email) || 'Guest'}","data-username": "#{(current_account.try(:user).try(:first_name).to_s.split('')[0].to_s + current_account.try(:user).try(:last_name).to_s.split('')[0].to_s).presence || 'G'}", "data-review-id": "#{current_account.try(:user).try(:reviews).to_a.collect(&:id).first.presence || 'none'}"}
          Submit
        %a.close-reveal-modal{"aria-label" => "Close", href: "#", role: "button"} ×
  .tabbed-btn-row
    #product-btn.tabbed-btn
      Product Reviews
    -# #site-btn.tabbed-btn
    -#   Site Reviews

  #product_reviews.tabbed-view.all-products
    .review-row{style:"margin-bottom: 0% !important;"}
      #sort_btn.toggle-review-btn
        .best_designs_btn
          Sort by:
          %button.btn.btn-custom-blue.btn-md.toggle-btn
            Top Reviews

    -color_classes.shuffle!
    -@reviews.each_with_index do |review, index|
      .review-row
        .block{id: "block_#{review.id}"}
          .user-sign{style: "background-color: #{color_classes[index]} !important;"}
            %span.user-name
              -if review.user.present?
                -if review.user.image_url.present?
                  =image_tag review.user.image_url,class: 'user_image'
                -else
                  = review.user.first_name.present? ? review.user.first_name[0]+(review.user.last_name.present? ? review.user.last_name[0] : '') : 'G'
              -else
                G
          .design-img.margin-left-top
            -if review.try(:design).try(:master_image)
              =link_to designer_design_path(review.designer, review.design, ici:'review-page-source-mobile',  icn: "review-page-#{review.design.id}") do
                =image_tag(IMAGE_PROTOCOL + review.design.master_image.photo('thumb'), style: "margin-right:2px;border: 1px solid black;width:40px;height:40px;")
            -else
              .image_block_hidden
          .stars.margin-left-top
            .ratings-score
              .star.col-sm-12{dscore: review.rating.to_i}
            .user-name-full.row
              .col-sm-12
                = review.user.present? ? review.reviewers_name : 'Guest Review'
            .rated-ago.row
              .col-sm-12
                = time_ago_in_words(review.updated_at) + " ago"
          .review-text.margin-left-top
            -if review.review.to_s.length > 150
              -show_view_more =true
              = truncate(review.review.to_s, length: 200)
            -else
              -show_view_more =false
              =review.review.to_s
          -if show_view_more
            %a{"data-reveal-id" => "view_more_content_#{review.id}", href: "#"}
              .toggle_scroll.view-more-btn
                view more
            #view_more_content.reveal-modal.black-content{id: "#{review.id}", "aria-hidden" => "true", "aria-labelledby" => "modalTitle", "data-reveal" => "", role: "dialog"}
              %h4#modalTitle
                = review.user.present? ? review.reviewers_name : 'Guest Review'
              .rated-ago.row
                .col-sm-12
                  = time_ago_in_words(review.updated_at) + " ago"
              #modal_star.stars.margin-left-top
                .ratings-score
                  .star.col-sm-12{dscore: review.rating.to_i}
              .modal-review-text
                %p
                  =review.review
              %a.close-reveal-modal{"aria-label" => "Close", href: "#", role: "button"} × 
    .review-row
      = paginate @reviews, total_pages: 10, params: {site_page: nil,reviewed_best: nil}
  #product_reviews.tabbed-view.best-products
    .review-row{style:"margin-bottom: 0% !important;"}
      #sort_btn.toggle-review-btn
        .best_designs_btn
          Sort by:
          %button.btn.btn-custom-blue.btn-md.toggle-btn 
            Top Reviews
    -@best_reviewed_designs.each_with_index do |review, index|
      .review-row
        .block
          .user-sign{style: "background-color: #{color_classes[index]} !important"}
            %span.user-name
              -if review.user.present?
                -if review.user.image_url.present?
                  =image_tag "#{review.user.image_url}",class: 'user_image'
                -else
                  = review.user.first_name.present? ? review.user.first_name[0]+(review.user.last_name.present? ? review.user.last_name[0] : '') : 'G'
              -else
                G  
          .design-img.margin-left-top
            -if review.try(:design).try(:master_image)
              =link_to designer_design_path(review.designer, review.design, ici:'review-page-source-mobile',  icn: "review-page-#{review.design.id}") do
                =image_tag(IMAGE_PROTOCOL + review.design.master_image.photo('thumb'), style: "margin-right:2px;border: 1px solid black;width:40px;height:40px;")
            -else
              .image_block_hidden
          .stars.margin-left-top
            .ratings-score
              .star.col-sm-12{dscore: review.rating.to_i}
            .user-name-full.row
              .col-sm-12
                = review.user.present? ? review.reviewers_name : 'Guest Review'
            .rated-ago.row
              .col-sm-12
                = time_ago_in_words(review.updated_at) + " ago"
          .review-text.margin-left-top
            =review.review.to_s

    .review-row
      = paginate @best_reviewed_designs, total_pages: 10, param_name: :reviewed_best,params: {site_page: nil,page: nil}
  #site_reviews.tabbed-view
    #hidden_site_review.block
      .user-sign{style: "background-color: #{color_classes[5]} !important"}
        %span#current_username.user-name 
      .stars.margin-left-top
        .ratings-score
          #current_rating.star.col-sm-12
        .user-name-full.row
          #current_email.col-sm-12
        .rated-ago.row
          #current_time_ago.col-sm-12
            Few Seconds ago
      #current_review.review-text.margin-left-top  
    .review-row{style:"margin-bottom: 0% !important;"}

    #upper_review_block.review-row
    -color_classes.shuffle!
    -@site_reviews.each_with_index do |review, index|
      .review-row
        .block{id: "#{review.id}"}
          .user-sign{style: "background-color: #{color_classes[index]} !important;"}
            %span.user-name
              -if review.user.present?
                -if review.user.image_url.present?
                  =image_tag "#{review.user.image_url}",class: 'user_image'
                -else
                  = review.user.first_name.present? ? review.user.first_name[0]+(review.user.last_name.present? ? review.user.last_name[0] : '') : 'G'
              -else
                G
          .stars.margin-left-top
            .ratings-score
              .star.col-sm-12{dscore: review.rating.to_i}
            .user-name-full.row
              = review.user.present? ? review.reviewers_name : 'Guest Review'
            .rated-ago.row
              .col-sm-12    
                = time_ago_in_words(review.updated_at) + " ago"
          .review-text.margin-left-top
            -if review.review.to_s.length > 150
              -show_view_more =true
              = truncate(review.review.to_s, length: 200)
            -else
              -show_view_more =false
              =review.review.to_s
          -if show_view_more
            %a{"data-reveal-id" => "view_more_content_#{review.id}", href: "#"}
              .toggle_scroll.view-more-btn
                view more
            #view_more_content.reveal-modal.black-content{id: "#{review.id}", "aria-hidden" => "true", "aria-labelledby" => "modalTitle", "data-reveal" => "", role: "dialog"}
              %h4#modalTitle
                = review.user.present? ? review.user.email : 'Guest'
              .rated-ago.row
                .col-sm-12
                  = time_ago_in_words(review.updated_at) + " ago"
              #modal_star.stars.margin-left-top
                .ratings-score
                  .star.col-sm-12{dscore: review.rating.to_i}
              .modal-review-text
                %p
                  =review.review
              %a.close-reveal-modal{"aria-label" => "Close", href: "#", role: "button"} ×
    .review-row      
      = paginate @site_reviews, total_pages: 10, param_name: :site_page,params: {page: nil, reviewed_best: nil}
= javascript_include_tag "jquery.raty.js"
= javascript_include_tag "reviews"
-unless params[:page].present? || params[:site_page].present? || params[:reviewed_best].present?
  -if @seo.present? && @seo.top_content.present?
    #site_review_top_content
      #top_content.read-more=raw @seo.top_content
      %center
        %a#view-more-top-content Read More
:javascript
  $('.star').raty({readOnly: true, score: function() {return $(this).attr('dscore');},
    starOff: "#{asset_path('star-off.jpg')}",
    starOn: "#{asset_path('star-on.jpg')}",
    starHalf: "#{asset_path('star-half.jpg')}",
    path: ''});
- if params[:site_page].present?
  :javascript
    $('#product_reviews').hide();
    $('.review-container .tabbed-btn-row #product-btn').removeClass("active-btn").addClass("passive-btn")
    $('.review-container .tabbed-btn-row #site-btn').addClass("active-btn").removeClass("passive-btn")
    $('#site_reviews').show();
- if params[:reviewed_best].present?
  :javascript
    $('#site_reviews,.all-products').hide();
    $('.review-container .tabbed-btn-row #site-btn').addClass("passive-btn")
    $('.best-products').show();
    $('.toggle-btn').text('All Reviews')
-else
  :javascript
    $('.best-products').hide();
    $('.toggle-btn').text('Top Reviews')
-if @review.present?
  :javascript
    $('#review-text').val("#{@review.review}")
    $('#form-star.star').raty({score: function() {return "#{@review.rating}";},
    starOff: "#{asset_path('star-off.jpg')}",
    starOn: "#{asset_path('star-on.jpg')}",
    starHalf: "#{asset_path('star-half.jpg')}",
    path: ''});
-else
  :javascript
    $('#form-star.star').raty({score: function()  {return $(this).attr('dscore');},
    starOff: "#{asset_path('star-off.jpg')}",
    starOn: "#{asset_path('star-on.jpg')}",
    starHalf: "#{asset_path('star-half.jpg')}",
    path: ''});