- if @seo.present?
  - title_key_word = params[:page] || params[:reviewed_best] || params[:site_page]
  - if title_key_word
    - title  "Page #{title_key_word} of #{@seo.title.to_s}"
    - keywords 'mirraw reviews'
    - description  "Searching for solution of your problems? Drop us your feedback here or Call us on #{MIRRAW_CONTACT_INFO} now to resolve your queries."
  -else
    - title  @seo.title.to_s
    - description @seo.description.to_s
    - keywords  @seo.keyword.to_s

= stylesheet_link_tag "rating_review"
-color_classes = ['#FF5252', '#64B5F6', '#81C784', '#FF8A65', '#90A4AE', '#AED581', '#4DD0E1', '#9575CD', '#8D6E63', '#757575']
.review-container.row
  .tabbed-btn-row
    #product-btn.tabbed-btn
      %h1 Product Reviews
    -# #site-btn.tabbed-btn
    -#   Site Reviews

  #product_reviews.tabbed-view.all-products
    -color_classes.shuffle!
    .product_row
      .design-img.margin-left-top
        -if @reviews.first.try(:design).try(:master_image)
          .product_images
            =link_to designer_design_path(@reviews.first.designer, @reviews.first.design, ici:'review-page-source-mobile',  icn: "review-page-#{@reviews.first.design.id}") do
              =image_tag(IMAGE_PROTOCOL + @reviews.first.design.master_image.photo('large'))
        -else
          .product_images
            .image_block_hidden
      .rating_review_box    
        -@reviews.each_with_index do |review, index|
          .review-row
            .block.review_box{id: "block_#{review.id}"}
              .user_information
                .user-sign{style: "background-color: #{color_classes[index]} !important;"}
                  %span.user-name
                    -if review.user.present?
                      -if review.user.image_url.present?
                        =image_tag review.user.image_url,class: 'user_image'
                      -else
                        = review.user.first_name.present? ? review.user.first_name[0]+(review.user.last_name.present? ? review.user.last_name[0] : '') : 'G'
                    -else
                      G
                .stars.margin-left-top
                  .ratings-score
                    .star.col-sm-12{dscore: review.rating.to_i}
                  .user-name-full
                    .col-sm-12
                      %p
                        = review.user.present? ? review.reviewers_name : 'Guest Review'
                  .rated-ago
                    .col-sm-12
                      %p
                        = time_ago_in_words(review.updated_at) + " ago"
              .review-text.margin-left-top
                -if review.review.to_s.length > 150
                  %p
                    -show_view_more =true
                    = truncate(review.review.to_s, length: 200)
                -else
                  %p
                    -show_view_more =false
                    =review.review.to_s
              -if show_view_more
                %a.view_more{"data-reveal-id" => "view_more_content_#{review.id}", href: "#"}
                  .toggle_scroll.view-more-btn
                    Read More
                #view_more_content.reveal-modal.black-content{id: "#{review.id}", "aria-hidden" => "true", "aria-labelledby" => "modalTitle", "data-reveal" => "", role: "dialog"}
                  %h4#modalTitle
                    = review.user.present? ? review.reviewers_name : 'Guest Review'
                  .rated-ago
                    .col-sm-12
                      = time_ago_in_words(review.updated_at) + " ago"
                  #modal_star.stars.margin-left-top
                    .ratings-score
                      .star.col-sm-12{dscore: review.rating.to_i}
                  .modal-review-text
                    %p
                      =review.review
                  %a.close-reveal-modal{"aria-label" => "Close"} × 
    .review-row
      = paginate @reviews, params: {site_page: nil,reviewed_best: nil}
  #product_reviews.tabbed-view.best-products
    .review-row{style:"margin-bottom: 0% !important;"}
      #sort_btn.toggle-review-btn
        .best_designs_btn
          Sort by:
          %button.btn.btn-custom-blue.btn-md.toggle-btn 
            Top Reviews
    .product_row        
      .design-img.margin-left-top
        -if @best_reviewed_designs.first.try(:design).try(:master_image)
          .product_images
            =link_to designer_design_path(@best_reviewed_designs.first.designer, @best_reviewed_designs.first.design, ici:'review-page-source-mobile',  icn: "review-page-#{@best_reviewed_designs.first.design.id}") do
              =image_tag(IMAGE_PROTOCOL + @best_reviewed_designs.first.design.master_image.photo('large'))
        -else
          .product_images
      .rating_review_box    
        -@best_reviewed_designs.each_with_index do |review, index|
          .review-row
            .block.review_box
              .user_information
                .user-sign{style: "background-color: #{color_classes[index]} !important"}
                  %span.user-name
                    -if review.user.present?
                      -if review.user.image_url.present?
                        =image_tag "#{review.user.image_url}",class: 'user_image'
                      -else
                        = review.user.first_name.present? ? review.user.first_name[0]+(review.user.last_name.present? ? review.user.last_name[0] : '') : 'G'
                    -else
                      G  
                      .image_block_hidden
                .stars.margin-left-top
                  .ratings-score
                    .star.col-sm-12{dscore: review.rating.to_i}
                  .user-name-full
                    .col-sm-12
                      = review.user.present? ? review.reviewers_name : 'Guest Review'
                  .rated-ago
                    .col-sm-12
                      = time_ago_in_words(review.updated_at) + " ago"
              .review-text.margin-left-top
                =review.review.to_s

    .review-row
      = paginate @best_reviewed_designs, param_name: :reviewed_best,params: {site_page: nil,page: nil}
= javascript_include_tag "jquery.raty.js"
= javascript_include_tag "reviews"
-unless params[:page].present? || params[:site_page].present? || params[:reviewed_best].present?
  -if @seo.present? && @seo.top_content.present?
    #site_review_top_content
      #top_content.read-more=raw @seo.top_content
      %center
        %a#view-more-top-content Read More
:javascript
  $('.star').raty({readOnly: true, score: function() {return $(this).attr('dscore');},
    starOff: "#{asset_path('star-off.jpg')}",
    starOn: "#{asset_path('star-on.jpg')}",
    starHalf: "#{asset_path('star-half.jpg')}",
    path: ''});
- if params[:site_page].present?
  :javascript
    $('#product_reviews').hide();
    $('.review-container .tabbed-btn-row #product-btn').removeClass("active-btn").addClass("passive-btn")
    $('.review-container .tabbed-btn-row #site-btn').addClass("active-btn").removeClass("passive-btn")
    $('#site_reviews').show();
- if params[:reviewed_best].present?
  :javascript
    $('#site_reviews,.all-products').hide();
    $('.review-container .tabbed-btn-row #site-btn').addClass("passive-btn")
    $('.best-products').show();
    $('.toggle-btn').text('All Reviews')
-else
  :javascript
    $('.best-products').hide();
    $('.toggle-btn').text('Top Reviews')
-if @review.present?
  :javascript
    $('#review-text').val("#{@review.review}")
    $('#form-star.star').raty({score: function() {return "#{@review.rating}";},
    starOff: "#{asset_path('star-off.jpg')}",
    starOn: "#{asset_path('star-on.jpg')}",
    starHalf: "#{asset_path('star-half.jpg')}",
    path: ''});
-else
  :javascript
    $('#form-star.star').raty({score: function()  {return $(this).attr('dscore');},
    starOff: "#{asset_path('star-off.jpg')}",
    starOn: "#{asset_path('star-on.jpg')}",
    starHalf: "#{asset_path('star-half.jpg')}",
    path: ''});