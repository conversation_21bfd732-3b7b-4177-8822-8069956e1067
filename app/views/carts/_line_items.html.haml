- bmgnx_active = (bmgnx_hash = PromotionPipeLine.bmgnx_hash).present? && line_item.buy_get_free == 1
- out_stock_design = line_item.available_quantity(@country_code) < line_item.quantity
- designr_name = line_item.design.designer.name.titleize
.item_block{id: "item_#{line_item.id}" ,style: "#{'border: 2px solid #e0356f;' if out_stock_design}"}
  .row.collapse
    .large-2.medium-3.small-3.columns.image-box
      = link_to designer_design_path(line_item.design.designer, line_item.design) do
        - if line_item.design.master_image.present?
          = image_tag(IMAGE_PROTOCOL + line_item.design.master_image.photo(:large), alt: line_item.design.title)
        - else
          = image_tag('default_image.jpg', alt: line_item.design.title)
      .truncate.m_web= designr_name
    .large-10.medium-9.small-9.columns
      .row.collapse
        - if !account_signed_in?
          = link_to line_item_path(line_item), method: 'DELETE', remote: true, unbxdattr: "RemoveFromCart", unbxdparam_qty: "1",  unbxdparam_sku: line_item.design.id, unbxdparam_requestId: session[:unbxdparam_requestId],  unbxdparam_price: line_item.snapshot_price, class: 'close right remove_from_cart' do
            &times;
        -if out_stock_design
          %span.out_of_stock Out of Stock
        .design-title= line_item.design.title.titleize
        .desk_web= designr_name
      - if line_item.variant.present?
        .row
          - option_type_value = line_item.variant.option_type_values.first
          = "#{option_type_value.option_type.p_name} : #{option_type_value.p_name}"
      .row.font-gray
        - price = get_price_with_symbol(line_item.snapshot_price_currency(@rate), @hex_symbol)
        .left Price :
        .right.item-price-font.actual-item-price= price
      - if line_item.line_item_addons.present?
        - @total_addon = 0
        = render partial: 'carts/line_item_addons', locals: {line_item: line_item}
      .row
        - total = get_price_with_symbol(line_item.total_currency(@rate), @hex_symbol)
        .large-2.medium-2.small-5.columns.design_quantity
          - design_quantity = line_item.max_quantity_on_cart
          - design_quantity = 10 if design_quantity > 10
          Qty &nbsp;
          = select_tag "quantity_#{line_item.id}", options_for_select((1..design_quantity).to_a, line_item.quantity), class: 'quantity_list'
        .large-10.medium-10.small-7.columns.right.item-price-font.cart-total= total
      - if bmgnx_active
        .row
          =link_to Promotion.bmgnx_offer_message(bmgnx_hash), '/buy-m-get-n-free', title: "View More - #{Promotion.bmgnx_offer_message(bmgnx_hash)} Products", class: 'b1g1'
          &nbsp;
          - message = "Buy #{bmgnx_hash[:m]} #{'item'.pluralize(bmgnx_hash[:m])} and get "
          - message += bmgnx_hash[:x] != 100 ? "#{bmgnx_hash[:x]}% off on next #{bmgnx_hash[:n]} #{'item'.pluralize(bmgnx_hash[:n])}" : "next #{bmgnx_hash[:n]} #{'item'.pluralize(bmgnx_hash[:n])} free"
          %a.b1g1_text{href:"#", 'data-reveal-id' => "bmgnTncModal", title: "How to avail this offer : #{message}"} i
      -if bmgnx_active && bmgnx_free_message.present?
        .row.free_item_message
          .b1g1_colored= bmgnx_free_message
    -# - if bmgnx_active
    -#   .large-12.columns.b1g1_info
  - if account_signed_in?
    .row.action-buttons-container.collapse
      .small-6.columns.action-button-cart.action-button
        = link_to 'Remove', line_item_path(line_item), method: 'DELETE', remote: true, title: 'Remove From Cart',unbxdparam_qty: "1",  unbxdattr: "RemoveFromCart", unbxdparam_sku: line_item.design.id, unbxdparam_requestId: session[:unbxdparam_requestId], unbxdparam_price: line_item.snapshot_price, class: 'remove_from_cart'
      .small-6.columns.action-button-wishlist.action-button
        = link_to "MOVE TO WISHLIST", line_item_path(line_item, move_to_wishlist: true), method: :delete, remote: true, title: 'Move To Wishlist', class: 'move_to_wishlist'
  :css
    .out_of_stock {
      display:table-cell;
      background-color: red;
      width:80px;
      height:5px;
      text-align:center;
      font-size:10px;
    }
