%tr.coupon-row
  
  %td.coupon-details
    .coupon-image
      %img{src: asset_path('coupon_default_image.jpg'), alt: "Coupon Image"}
    .coupon-description
      .coupon-percent
        - if coupon.flat_off.present? && coupon.coupon_type == 'FOFF'
          You'll get #{get_price_with_symbol(get_price_in_currency(coupon.flat_off), @hex_symbol)} off
        - elsif coupon.percent_off.present? && coupon.coupon_type == 'POFF'
          You'll get #{coupon.percent_off}% off
        - elsif coupon.present? && ['SHIPOFF','STITOFF'].include?(coupon.coupon_type)
          #{coupon_message_based_on_type(coupon.coupon_type)}
          
      .min-amount 
        This offer is valid on minimum purchase of #{get_price_with_symbol(get_price_in_currency(coupon.min_amount), @hex_symbol)}
      .expiry-date 
        Expiry date: #{coupon.end_date.strftime("%d-%m-%Y")}
  %td.coupon-code-div
    .coupon-code= coupon.code
    .input-div
      %input{type: "submit", value: "Apply", class: "button btn-apply apply-button","data-coupon-code": coupon.code, id: "applyButton"} 