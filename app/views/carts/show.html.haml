.row.flash-message
  - zurbs_flash_classes = {'notice' => 'success', 'alert' => 'warning', 'error' => 'alert'}
  - flash.each do |key, msg|
    .alert-box.radius{data: {alert: ''}, class: zurbs_flash_classes[key]}
      = msg
      %a{href: '#', class: 'close'} &times;
- content_for :page_specific_css do
  = stylesheet_link_tag 'carts'
- content_for :page_specific_js do
  = javascript_include_tag 'carts'
  = javascript_include_tag 'product_carts'
=render partial: '/layouts/initialise_grading'
#carts_block
  - if @cart.line_items.present?
    .row.panel_block
      %span.shopping_heading Your Shopping Cart
      .columns.panel_heading.save_cart.m_web
        = render partial: 'carts/save_cart_email'
      - best_offer_text, discount_perc, discount_rates, offers_present_for_country = get_best_offer_for_cart_text(@cart, @rate, @actual_country, @country_code, get_symbol_from(@hex_symbol))
      - if best_offer_text.present?
        .columns
          .small-12.columns.best_offer
            .info_icon i
            %span
              = best_offer_text
      - if (bmgnx_hash = PromotionPipeLine.bmgnx_hash).present?
        = render :partial => '/designs/b1g1_tnc_in_detail', locals: {bmgnx_hash: bmgnx_hash}
        -bmgnx_offer_msg = @cart.get_bmgnx_notice(bmgnx_hash: bmgnx_hash)
        - if bmgnx_offer_msg
          .columns.design_offer_panel_content
            .small-12.columns.best_offer
              .design_offer_cart_offer_msg
                = link_to " B#{bmgnx_hash[:m]}G#{bmgnx_hash[:n]} scheme :",'/buy-m-get-n-free' ,class: 'hyperlink_text'
                = " #{bmgnx_offer_msg}"
      - elsif (next_qpm_msg = @cart.next_qpm_msg).present?
        .columns.design_offer_panel_content
          .small-12.columns.best_offer
            .design_offer_cart_offer_msg
              = next_qpm_msg
      .columns.panel_content
        - item_total = @cart.item_total(@rate)
        - discounted_item_total = get_price_in_currency(@cart.item_total(1) - @cart.additional_discounts(@actual_country), @rate).round(2)
        - bmgnx_discounts = get_price_in_currency(@cart.bmgnx_discounts(bmgnx_hash))
        - if !is_domestic? && discounted_item_total < @cart_minimum_value.to_f
          .minimum_cart_value_message
            International Orders should have a minimum order value of #{@cart_minimum_value}. Your current order value is #{@symbol} #{discounted_item_total} (shipping charges excluded)
        - no_of_items = 0
        - for line_item in @cart.line_items
          - no_of_items += line_item.quantity
        %span.item_count Items (#{no_of_items})
        - recent_line_item = @cart.line_items.first
        - if recent_line_item
          - catgory = recent_line_item.design.categories.preload(:parent).first
          - category_name = catgory.parent.present? ? catgory.parent.name : catgory.name
        - recent_url = category_name.present? ? "/store/#{category_name}" : Frontpage.topmost_link
        - line_item_disc_message = @cart.line_item_bmgnx_discount_message
        - cart_addon_design = @cart.cart_addon_designs(@country_code)
        - items = @cart.line_items - @cart.cart_addon_item(@country_code)
        .row
          .columns.large-9
            - for line_item in items
              = render partial: 'carts/line_items', locals: {line_item: line_item, bmgnx_free_message: line_item_disc_message[line_item.id]}
              -if (addons = cart_addon_design[line_item.design.designable_type]).present?
                = render partial: 'carts/cart_addon_design', locals: {cart_addon_design: addons}
                - cart_addon_design[line_item.design.designable_type] = nil
          .columns.large-3.cart_right_side_bar
            - if GIFT_WRAP_PRICE.to_f >= 0 && @country_code != 'IN'
              = render partial: 'carts/gift_wrap'
            - grandtotal = @cart.total_currency(@rate, @country_code, @actual_country)
            - grandtotal_without_shipping = @cart.total_currency(@rate, @country_code, nil, false, false)
            = render partial: 'carts/cart_coupon', locals: { country: @actual_country, cart_sub_total: grandtotal_without_shipping}
            = render partial: 'carts/totals', locals: { country: @actual_country }
            - grandtotal += (GIFT_WRAP_PRICE.to_f/@rate).round(2) if session[:gift_wrap]
            - _,tax_amount,grandtotal,tax_status = @cart.get_total_with_tax(@country_code, grandtotal)
            - amount_to_credit = Wallet.cashback_for_amount(grandtotal)
            - pro_totals, max_total, shipping_currency = get_cart_total_information(@country_code, @rate)
            - coupon_discount_amount = pro_totals.find { |item| item[:title] == "Coupon Discounts" }&.dig(:amount) || 0
            - total_amount = pro_totals.find { |item| item[:title] == "Item Total" }&.dig(:amount) || 0
            - tax_amount = pro_totals.find { |item| item[:title] == "Tax" }&.dig(:amount) || 0
            - gift_wrap = session[:gift_wrap] && @country_code != 'IN' ? get_price_in_currency(GIFT_WRAP_PRICE.to_f) : 0
            - if (market_rate = CurrencyConvert.countries_marketrate[@country_code]).present?
              - gift_wrap = (gift_wrap *= (market_rate).round(CurrencyConvert.round_to)).round(CurrencyConvert.round_to)
              - shipping_currency = (shipping_currency *= (market_rate).round(CurrencyConvert.round_to)).round(CurrencyConvert.round_to)
              - coupon_discount_amount = (coupon_discount_amount *= (market_rate).round(CurrencyConvert.round_to)).round(CurrencyConvert.round_to)
              - tax_amount = (tax_amount *= (market_rate).round(CurrencyConvert.round_to)).round(CurrencyConvert.round_to)
            - if coupon_discount_amount != 0
              - discount_percent = (coupon_discount_amount*100.0/@totalvalue)
            - else
              - discount_percent = 0
            #action_buttons_desk.cart-checkout-btn.desk_web
              = render partial: 'cart_checkout', locals: {sticky_checkout: false}
            - if offers_present_for_country.present? || (item_total < @cart_minimum_value.to_f) || amount_to_credit.to_i > 0 || is_domestic?
              .row
                .columns.cart-discount-message
                  %br
                  %h5 Available Offers :
                  .offer_messages_block
                    %ul
                      - if amount_to_credit.to_i > 0
                        %li
                          You will get cashback worth #{get_symbol_from(@hex_symbol)}#{amount_to_credit.round(2)} in your mirraw wallet.
                      - if is_domestic?
                        - msg = DOMESTIC_PREPAID_SHIPPING_PROMOTION ? 'Cash on Delivery' : ''
                        - if DOMESTIC_PREPAID_SHIPPING_PROMOTION
                          %li= "Get <span style='color:RGB(56,118,29);font-weight:bold'>FREE</span> Shipping on Online Payment".html_safe
                        - if DOMESTIC_SHIPPING_CHARGES.keys.last.to_i > 0
                          %li Free Shipping on #{msg} Orders above #{get_symbol_from(@hex_symbol)}#{DOMESTIC_SHIPPING_CHARGES.keys.last.to_i}
                        - else
                          %li Free Shipping on all Orders
                      - if !is_domestic?
                        - if @country_code != 'IN'
                          - if Promotion.free_shipping_on_country?
                            = render partial: 'carts/promotion_offers', locals: { offers: @offers }
                        - if @free_stitching_text.present?
                          %li= @free_stitching_text
                      - max_discount = 0
                      - if !@cart.coupon.present?
                        - discount_rates.each_with_index do |amount,index|
                          - amount_on = get_price_in_currency(discount_rates[index].to_i, @rate)
                          - if item_total - bmgnx_discounts <= amount_on
                            %li= "Extra #{discount_perc[index]}% off above #{get_symbol_from(@hex_symbol)}#{number_with_delimiter(amount_on)}"
                          - elsif max_discount < discount_perc[index].to_i && BEST_OFFER_THRESHOLD.to_i >= 0
                            - max_discount = discount_perc[index].to_i
                        - if max_discount > 0
                          %li= "You got extra #{max_discount}% off"             
            - if BEST_OFFER_THRESHOLD.to_i < 0
              #action_buttons.cart_checkout.m_web
                = render partial: 'cart_checkout', locals: {sticky_checkout: false}
            #action_buttons.shop.columns
              - if is_domestic? && !PAY_WITH_AMAZON_DISABLED && @cart.has_minimun_quantity && @cart.wallet_discounts(Design.country_code, @rate) == 0
                = render partial: 'carts/pay_with_amazon'
              = link_to 'Continue Shopping', recent_url , class: "add_cont_shop button small text-center #{'improved_link' unless check_for_alternate_tab?}"
            .columns.panel_heading.save_cart.desk_web
              = render partial: 'carts/save_cart_email_desktop'
              %br
      - if BEST_OFFER_THRESHOLD.to_i >= 0
        .sticky-button#fixed_checkout_button
          .columns.panel_heading.m_web
            .small-6.columns
              .view_details_button.button
                = "#{get_price_with_symbol(grandtotal, @hex_symbol)}"
                .view_details_text
                  VIEW DETAILS
            .small-6.columns.cart-checkout-btn
              = render partial: 'cart_checkout', locals: {sticky_checkout: true}
    .row.panel_block{style: "background-color: transparent;box-shadow: none;"}
      .columns{style: 'padding: 0px'}
        - if UnbxdContainer.active?('Cart Page Mobile > Bottom Horizontal Container')
          = load_unbxd_container('Cart Page Mobile > Bottom Horizontal Container')
  - else
    = render partial: 'no_items_cart'
:javascript
  $(function(){$('.orbit-timer').hide();});
  $(document).foundation({
    orbit: {
      slide_number: false,
      navigation_arrows: false,
      bullets: false,
      pause_on_hover: true,
      timer_speed: 4000,
      resume_on_mouseout: true,
    }
  });

- if @cart.line_items.where(buy_get_free: 1).sum(:quantity) ==1
  :javascript
    title = $('.b1g1_text').attr('title');
    $('.b1g1_info').append('<a href="/buy1get1-offers">' + $('.b1g1_text').attr("title") + '</a>');

-if @cart.line_items.sum(:quantity) != 0
  :javascript
    window.dataLayer = window.dataLayer || [];
    var total =  (#{@totalvalue} + #{gift_wrap}) - #{coupon_discount_amount}
    var ga4_view_cart_params = #{@ga_hash_new.to_json};
    var customizationTotal = 0;
    ga4_view_cart_params.items.forEach(function (item) {
      customizationTotal += item.item_customization * item.quantity;
    });
    ga4_view_cart_params.customization = customizationTotal
    ga4_view_cart_params.tax = #{tax_amount}
    ga4_view_cart_params.shipping = #{shipping_currency}
    ga4_view_cart_params.value = total + customizationTotal
    ga4_view_cart_params.gift_wrap = #{gift_wrap}
    ga4_view_cart_params.coupon_discount = #{coupon_discount_amount}
    dataLayer.push({ ecommerce: null });
    dataLayer.push({
      event: "ga4_view_cart",
      ecommerce: ga4_view_cart_params
    });

    $(document).ready(function() {
        PRODUCT_CART_INFO.events.init(ga4_view_cart_params)
    });

