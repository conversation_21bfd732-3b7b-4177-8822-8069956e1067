- content_for :page_specific_css do
  = stylesheet_link_tag 'sessions'

.row
  .large-6.medium-8.small-12.columns.small-centered
    %h4 Resend confirmation instructions
    .row.bordered_block
      = form_for(resource, as: resource_name, url: confirmation_path(resource_name), html: { method: :post }) do |f|
        = f.email_field :email, autofocus: true, value: (resource.pending_reconfirmation? ? resource.unconfirmed_email : resource.email), placeholder: "Enter Email", label: false, required: true
        = f.submit "Resend confirmation instructions", class: 'radius expand reset-password button small'
    = render "devise/shared/links"