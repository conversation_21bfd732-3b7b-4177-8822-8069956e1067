%dl#FAQs.accordion{itemscope: true, itemtype: "https://schema.org/FAQPage", "data-accordion" => ""}
  %dt FAQs
  - faqs.reverse.each_with_index do |faq, index|
    - id, question, answer = faq['id'], faq['question'], faq['answer']
    %dd.accordion-navigation{itemprop: "mainEntity", itemscope: true, itemtype: "https://schema.org/Question"}
      %a{href: "#faqpanel#{id}"}
        %h4{itemprop: "name"}=raw question
      .content{id: "faqpanel#{id}", class: index.zero? ? 'active' : '', itemprop: "acceptedAnswer", itemscope: true, itemtype: "https://schema.org/Answer"}
        %span{itemprop: "text"}=raw answer
