- index = request.path.index(params[:facets]) - 2 if params[:facets].present?
- page_base_url = index ? request.path[0..index] : request.path
- facets_info = FACETED_URL_KINDS[params[:kind]]
- if facets_info
  - url_params = create_url_parameters(@facet_properties, params)
- rating_price = RATING_ENABLE ? ['rating','price','discount percent'] : ['price','discount percent']
.filter-accordion-wrapper
  -if @store_page['filters'].present?
    - if @store_page['filters']['range_filters'].present?
      %ul.accordion{"data-accordion" => ""}
        - @store_page['filters']['range_filters'].each.with_index(1) do |range_filter,range_index|
          - filter_name = range_filter['name'].try(:downcase).to_s
          - filter_name = 'price_range' if filter_name == 'price'
          - filter_name = 'discount_percent' if filter_name == 'discount percent'
          - data_attr = { property: range_filter['name'].downcase, property_key: range_filter['return_key'], return_type: range_filter['type']}
          - if rating_price.include?(range_filter['name'].try(:downcase))
            %li.accordion-navigation.accordion-item
              %a.accordion-header{href: "#accordionFilter#{filter_name}", class: "accordion-title"}
                %div.accordion-button.collapsed.filter-header-title{"aria-controls" => "accordionFilter#{filter_name}Collapse", "aria-expanded" => "true", "data-bs-target" => "#accordionFilter#{filter_name}Collapse", "data-bs-toggle" => "collapse", :type => "button"}
                  = range_filter_name(range_filter)
                  / <span class="active-dot"></span> <span class="clear-btn">Clear</span>
              .content.accordion-collapse.collapse{"aria-labelledby" => "accordionFilter#{filter_name}", "data-bs-parent" => "#accordionExample",id: "accordionFilter#{filter_name}"}
                %button.clear-all-btn{"data-filter-name" => filter_name, type: "button", "aria-label" => "Clear all filters"}
                  %span.clear-icon ×
                  %span.clear-text Clear All
                .accordion-body.filterCustomScrollbar
                  - if params['facet'].present? && (filter_keys = params['facet'].keys).present? && filter_keys.include?(range_filter["return_key"])
                    %input{id: "#{range_filter['keys']['min']}" ,class: 'range_filter_input' ,type: 'hidden' ,name: "#{range_filter['keys']['min']}", value: params[range_filter['keys']['min']]}
                    %input{id: "#{range_filter['keys']['max']}" ,class: 'range_filter_input' ,type: 'hidden' ,name: "#{range_filter['keys']['max']}", value: params[range_filter['keys']['max']]}
                  - else
                    %input{id: "#{range_filter['keys']['min']}" ,class: 'range_filter_input' ,type: 'hidden' ,name: "#{range_filter['keys']['min']}"}
                    %input{id: "#{range_filter['keys']['max']}" ,class: 'range_filter_input' ,type: 'hidden' ,name: "#{range_filter['keys']['max']}"}
                  - if range_filter['list'].present?
                    - range_filter['list'].each do |list|
                      - formatted_list_name = list['name'].split('-').map { |n| "#{n.strip}%" }.join('-') if range_filter['name'].try(:downcase) == 'discount percent'
                      - chip_text = range_filter['name'].try(:downcase) == 'discount percent' ? "Discount - #{formatted_list_name}" : "#{range_filter['name']} - #{list['name']}"
                      .single_line_div
                        - if params['facet'].present? && params['facet'][data_attr[:property_key]].present? && params['facet'][data_attr[:property_key]]['value'].include?(list['name'])
                          .facet-link-desktop
                            .on-off-radiobox.switch-desk.tiny
                              %input.filter-select{name: "#{range_filter['name']}_range" ,value: list['name'] ,type: 'radio' ,id: "#{range_filter['name'].parameterize}_#{list['value']['min'].to_i}-#{list['value']['max'].to_i}", class: "#{range_filter['name'].parameterize}" ,checked: 'checked', data: {min_input: "#{range_filter['keys']['min']}" ,max_input: "#{range_filter['keys']['max']}", max: "#{list['value']['max'].to_i}" ,min: "#{list['value']['min'].to_i}", chip: chip_text}.merge(data_attr)}
                              %label{for: "#{range_filter['name'].parameterize}_#{list['value']['min'].to_i}-#{list['value']['max'].to_i}"}
                            %label.label-custom{for: "#{range_filter['name'].parameterize}_#{list['value']['min'].to_i}-#{list['value']['max'].to_i}", class: ('label-desktop-fix' unless browser.device.mobile?)}
                              = display_text(range_filter, list, get_symbol_from(@hex_symbol), formatted_list_name)
                              - if false #list['count'] > 0
                                %span.round.success.label= list['count']
                        - else
                          .facet-link-desktop
                            .on-off-radiobox.switch-desk.tiny
                              %input.filter-select{name: "#{range_filter['name']}_range" ,value: list['name'] ,type: 'radio' ,id: "#{range_filter['name'].parameterize}_#{list['value']['min'].to_i}-#{list['value']['max'].to_i}", class: "#{range_filter['name'].parameterize}" ,data: {min_input: "#{range_filter['keys']['min']}" ,max_input: "#{range_filter['keys']['max']}",max: "#{list['value']['max'].to_i}" ,min: "#{list['value']['min'].to_i}"}.merge(data_attr)}
                              %label{for: "#{range_filter['name'].parameterize}_#{list['value']['min'].to_i}-#{list['value']['max'].to_i}"}
                            %label.label-custom{for: "#{range_filter['name'].parameterize}_#{list['value']['min'].to_i}-#{list['value']['max'].to_i}", class: ('label-desktop-fix' unless browser.device.mobile?)}
                              = display_text(range_filter, list, get_symbol_from(@hex_symbol), formatted_list_name)
                              //%span.round.warning.label= list['count']
    - if @store_page['filters']['id_filters'].present?
      %ul.accordion{"data-accordion" => ""}
        - sorted_filters = @store_page['filters']['id_filters'].sort_by { |filter| filter['name'] }
        - sorted_filters.each.with_index(1) do |id_filter,index|
          - if allow_facet_for_view?(params[:kind], id_filter['name'], id_filter['priority'])
            - filter_name = id_filter['name'].try(:downcase).to_s.gsub(' ', '-')
            %li.accordion-navigation.accordion-item
              %a.accordion-header{href: "#accordionFilter#{filter_name}", class: "accordion-title"}
                %div.accordion-button.collapsed.filter-header-title{"aria-controls" => "accordionFilter#{filter_name}Collapse", "aria-expanded" => "true", "data-bs-target" => "#accordionFilter#{filter_name}Collapse", "data-bs-toggle" => "collapse", :type => "button"}
                  //%a.button.tiny.tab-filter-fix{href: "#tab_check_#{index}", id: "#{ids_filter['name'].parameterize}"}
                  = id_filter['name']
                  - if false #(filter_keys = params[id_filter['key']]).present?
                    - active_filter = get_active_filters_count(id_filter['list'].map{|x| x['value']}, filter_keys)
                    - if active_filter > 0
                      %span.tiny-green= "#{active_filter}"

              .content{"aria-labelledby" => "accordionFilter#{filter_name}", "data-bs-parent" => "#accordionExample",id: "accordionFilter#{filter_name}"}
                %button.clear-all-btn{"data-filter-name" => filter_name}
                  %span.clear-icon ×
                  %span.clear-text Clear All
                .filter-search-input-field
                  %i.fi-magnifying-glass
                  %input.search-field{type: 'text', placeholder: "Search #{filter_name}"}
                .accordion-body.filterCustomScrollbar.mCustomScrollbar 
                  - if id_filter['list'].present?
                    - data_attr = { property: id_filter['name'].downcase, property_key: id_filter['return_key'], return_type: id_filter['type'] }
                    - id_filter['list'].each do |list|
                      - box_type = id_filter['key'] == 'property_value_ids' && facets_info && !id_filter['name'].match(/color/i).present? ? 'radio' : 'checkbox'
                      - box_type = id_filter['key'] == 'Gender' ? 'radio': 'checkbox'
                      - if facets_info
                        - current_prop = { name: list['value'], priority: id_filter['priority'], type: box_type }
                        - data_attr.merge!(current_prop)
                        - if id_filter['priority'].to_i > 0 && create_url_for_facets?(box_type, @facet_properties)
                          - current_prop[:property] = id_filter['name']
                          - link = create_faceted_url(params[:kind], current_prop, @facet_properties, page_base_url) + url_params.to_s
                      - data_attr[:key] = "#{id_filter['key']}[]"
                      - if id_filter['name'].match(/color/i).present?
                        - color_val = (list['color_code'] == "-1") ? "null" : "rgba(#{list['color_code']})"
                        .single_line_div
                          - if params['facet'].present? && params['facet'][data_attr[:property_key]].present? && params['facet'][data_attr[:property_key]]['value'].include?(list['value'])
                            - data_attr[:chip] = "#{id_filter['name']} - #{list['name']}"
                            %a.facet-link-desktop{href: link}
                              .on-off-checkbox.color-switch.switch-desk
                                %input.filter-select.color-input{name: "#{id_filter['key']}-#{id_filter['name']}[]" ,id: "#{id_filter['key']}_#{list['value']}".gsub(' ', '_'),type: 'checkbox', placeholder: "#{list['name']}".downcase.tr(' ','-'), value: "#{list['value']}", checked: 'checked', class: [('color-append' if ['saree-color','kameez-color','color', 'lehenga-color'].include?(id_filter['name'].parameterize)),("#{id_filter['name'].parameterize}")], data: data_attr }
                                %label.label-custom-color-desktop{for: "#{id_filter['key']}_#{list['value']}", class: ('multicolor-value' if list['color_code'] == "-1"), style: "background: #{color_val}"}
                              %label.label-custom{for: "#{id_filter['key']}_#{list['value']}", class: ('label-desktop-fix' unless browser.device.mobile?)}
                                = list['name']
                                - if false #list['count'] > 0
                                  %span.round.success.label= list['count']
                          - else
                            %a.facet-link-desktop{href: link}
                              .on-off-checkbox.color-switch.switch-desk
                                %input.filter-select.color-input{name: "#{id_filter['key']}-#{id_filter['name']}[]" ,id: "#{id_filter['key']}_#{list['value']}".gsub(' ', '_') ,type: 'checkbox', placeholder: "#{list['name']}".downcase.tr(' ','-'), data: data_attr, value: "#{list['value']}", class: [("color-append" if ['saree-color','kameez-color','color', 'lehenga-color'].include?(id_filter['name'].parameterize)),("#{id_filter['name'].parameterize}")]}
                                %label.label-custom-color-desktop{for: "#{id_filter['key']}_#{list['value']}", class: ('multicolor-value' if list['color_code'] == "-1"), style: "background: #{color_val}"}
                              %label.label-custom{for: "#{id_filter['key']}_#{list['value']}", class: ('label-desktop-fix' unless browser.device.mobile?)}
                                = list['name']
                                - if false #list['count'] > 0
                                  %span.round.warning.label= list['count']
                      - else
                        - class_type = box_type == 'radio' ? 'on-off-radiobox' : 'on-off-checkbox'
                        .single_line_div
                          - if params['facet'].present? && params['facet'][data_attr[:property_key]].present? && params['facet'][data_attr[:property_key]]['value'].include?(list['value'])
                            - data_attr[:chip] = "#{id_filter['name']} - #{list['name']}"
                            %a.facet-link-desktop{href: link}
                              .switch-desk.tiny{class: class_type}
                                %input.filter-select{name: "#{id_filter['key']}-#{id_filter['name']}[]" ,id: "#{id_filter['key']}_#{list['value']}".gsub(' ', '_'),class: "#{id_filter['name'].parameterize}" ,type: box_type, value: "#{list['value']}", checked: 'checked', data: data_attr}
                                %label{for: "#{id_filter['key']}_#{list['value']}"}
                              %label.label-custom{for: "#{id_filter['key']}_#{list['value']}", class: ('label-desktop-fix' unless browser.device.mobile?)}
                                = list['name']
                                - if false #list['count'] > 0
                                  %span.round.success.label= list['count']
                          - else
                            %a.facet-link-desktop{href: link}
                              .switch-desk.tiny{class: class_type}
                                %input.filter-select{name: "#{id_filter['key']}-#{id_filter['name']}[]" ,id: "#{id_filter['key']}_#{list['value']}".gsub(' ', '_'),class: "#{id_filter['name'].parameterize}" ,type: box_type, value: "#{list['value']}", data: data_attr}
                                %label{for: "#{id_filter['key']}_#{list['value']}"}
                              %label.label-custom{for: "#{id_filter['key']}_#{list['value']}", class: ('label-desktop-fix' unless browser.device.mobile?)}
                                = list['name']
                                - if false #list['count'] > 0
                                  %span.round.warning.label= list['count']

                                  