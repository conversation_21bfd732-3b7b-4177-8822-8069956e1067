- index = request.path.index(params[:facets]) - 2 if params[:facets].present?
- page_base_url = index ? request.path[0..index] : request.path
- facets_info = FACETED_URL_KINDS[params[:kind]]
- if facets_info
  - url_params = create_url_parameters(@facet_properties, params)
#designable_details.row.panel_block
  -if params[:max_odr].present?
    .span#max-odr-parameter{data: {max_odr: params[:max_odr]}}
  - if @store_page['filters'].present?
    // array for displaying only important filters
    - rating_price = RATING_ENABLE ? ['rating','price','discount percent'] : ['price','discount percent']
    .columns.panel_heading.chips_maker
    .columns.small-4.panel_content
      %header
        %nav
          - if @store_page['filters']['range_filters'].present? && is_mobile_view?
            - @store_page['filters']['range_filters'].each.with_index(1) do |range_filter,range_index|
              - if rating_price.include?(range_filter['name'].try(:downcase))
                %a.button.tiny.tab-filter-fix{href: "#tab_#{range_index}", class: ('active-tab' if range_filter['name'] == 'price'), id: "#{range_filter['name'].parameterize}"}
                  = range_filter_name(range_filter)
                  - if params['facet'].present? && (filter_keys = params['facet'].keys).present? && filter_keys.include?(range_filter["return_key"])
                    %span.tiny-green= "1"
          - if (gender_filter = @store_page['filters']['gender_filter']).present?
            %a.button.tiny.tab-filter-fix{href: "#tab_#{gender_filter['position']}", id: "#{gender_filter['name'].parameterize}"}
              = gender_filter['name']
              - if params[gender_filter['key']]
                %span.tiny-green= "1"
          - if @store_page['filters']['id_filters'].present?
            - sorted_filters = @store_page['filters']['id_filters'].sort_by { |filter| filter['name'] }
            - sorted_filters.each.with_index(1) do |id_filter,index|
              - if allow_facet_for_view?(params[:kind], id_filter['name'], id_filter['priority'])
                %a.button.tiny.tab-filter-fix{href: "#tab_check_#{index}", id: "#{id_filter['name'].parameterize}"}
                  = id_filter['name']
                  - if params['facet'].present? && (filter_keys = params['facet'].keys).present? && filter_keys.include?(id_filter["return_key"])
                    - active_filter = params['facet'][id_filter["return_key"]]['value'].count
                    - if active_filter > 0
                      %span.tiny-green= "#{active_filter}"
                  - if (filter_keys = params[id_filter['key']]).present?
                    - active_filter = get_active_filters_count(id_filter['list'].map{|x| x['value']}, filter_keys)
                    - if active_filter > 0
                      %span.tiny-green= "#{active_filter}"
    .columns.small-8.panel_content.tabs-content
      - if @store_page['filters']['range_filters'].present? && is_mobile_view?
        - @store_page['filters']['range_filters'].each.with_index(1) do |range_filter,range_index|
          - data_attr = { property: range_filter['name'].downcase, property_key: range_filter['return_key'], return_type: range_filter['type']}
          - if rating_price.include?(range_filter['name'].try(:downcase))
            %div.content{id: "tab_#{range_index}", class: ('active' if params[:last_filter].present? && params[:last_filter] == "#{range_filter['name'].parameterize}") }
              - if params[range_filter['keys']['min']].present? && params[range_filter['keys']['max']].present?
                %input{id: "#{range_filter['keys']['min']}" ,class: 'range_filter_input' ,type: 'hidden' ,name: "#{range_filter['keys']['min']}", value: params[range_filter['keys']['min']]}
                %input{id: "#{range_filter['keys']['max']}" ,class: 'range_filter_input' ,type: 'hidden' ,name: "#{range_filter['keys']['max']}", value: params[range_filter['keys']['max']]}
              - else
                %input{id: "#{range_filter['keys']['min']}" ,class: 'range_filter_input' ,type: 'hidden' ,name: "#{range_filter['keys']['min']}"}
                %input{id: "#{range_filter['keys']['max']}" ,class: 'range_filter_input' ,type: 'hidden' ,name: "#{range_filter['keys']['max']}"}
              - if range_filter['list'].present?
                - range_filter['list'].each do |list|
                  - formatted_list_name = list['name'].split('-').map { |n| "#{n.strip}%" }.join('-') if range_filter['name'].try(:downcase) == 'discount percent'
                  .single_line_div{style: "margin-bottom: 0.5rem;"}
                    - if params['facet'].present? && params['facet'][data_attr[:property_key]].present? && params['facet'][data_attr[:property_key]]['value'].include?(list['name']) && is_mobile_view?
                      %a.facet-link{href: '#'}
                        .on-off-radiobox.switch.tiny
                          %input.filter-select{name: "#{range_filter['name']}_range" ,value: list['name'] ,type: 'radio' ,id: "#{range_filter['name'].parameterize}_#{list['value']['min'].to_i}-#{list['value']['max'].to_i}", class: "#{range_filter['name'].parameterize}" ,checked: 'checked', data: {min_input: "#{range_filter['keys']['min']}" ,max_input: "#{range_filter['keys']['max']}", max: "#{list['value']['max'].to_i}" ,min: "#{list['value']['min'].to_i}", chip: "#{range_filter['name']} - #{list['name']}"}.merge(data_attr)}
                          %label{for: "#{range_filter['name'].parameterize}_#{list['value']['min'].to_i}-#{list['value']['max'].to_i}"}
                        %label.label-custom{for: "#{range_filter['name'].parameterize}_#{list['value']['min'].to_i}-#{list['value']['max'].to_i}", class: ('label-desktop-fix' unless browser.device.mobile?)}
                          = display_text(range_filter, list, get_symbol_from(@hex_symbol), formatted_list_name)
                          - if list['count'] > 0
                            %span.round.success.label= list['count']
                    - else
                      %a.facet-link{href: '#'}
                        .on-off-radiobox.switch.tiny
                          %input.filter-select{name: "#{range_filter['name']}_range" ,value: list['name'] ,type: 'radio' ,id: "#{range_filter['name'].parameterize}_#{list['value']['min'].to_i}-#{list['value']['max'].to_i}", class: "#{range_filter['name'].parameterize}" ,data: {min_input: "#{range_filter['keys']['min']}" ,max_input: "#{range_filter['keys']['max']}",max: "#{list['value']['max'].to_i}" ,min: "#{list['value']['min'].to_i}"}.merge(data_attr)}
                          %label{for: "#{range_filter['name'].parameterize}_#{list['value']['min'].to_i}-#{list['value']['max'].to_i}"}
                        %label.label-custom{for: "#{range_filter['name'].parameterize}_#{list['value']['min'].to_i}-#{list['value']['max'].to_i}", class: ('label-desktop-fix' unless browser.device.mobile?)}
                          = display_text(range_filter, list, get_symbol_from(@hex_symbol), formatted_list_name)
                          %span.round.warning.label= list['count']
      - if @store_page['filters']['id_filters'].present?
        - sorted_filters = @store_page['filters']['id_filters'].sort_by { |filter| filter['name'] }
        - sorted_filters.each.with_index(1) do |id_filter ,index|
          - if allow_facet_for_view?(params[:kind], id_filter['name'], id_filter['priority'])
            %div.content{id: "tab_check_#{index}", class: ('active' if params[:last_filter].present? && params[:last_filter] == "#{id_filter['name'].parameterize}")}
              - if id_filter['list'].present?
                - data_attr = { property: id_filter['name'].downcase, property_key: id_filter['return_key'], return_type: id_filter['type'] }
                - id_filter['list'].each do |list|
                  - box_type = id_filter['key'] == 'property_value_ids' && facets_info && !id_filter['name'].match(/color/i).present? ? 'radio' : 'checkbox'
                  - box_type = id_filter['key'] == 'Gender' ? 'radio': 'checkbox'
                  - if facets_info
                    - current_prop = { name: list['value'], priority: id_filter['priority'], type: box_type }
                    - data_attr.merge!(current_prop)
                    - if id_filter['priority'].to_i > 0 && create_url_for_facets?(box_type, @facet_properties)
                      - current_prop[:property] = id_filter['name']
                      - link = create_faceted_url(params[:kind], current_prop, @facet_properties, page_base_url) + url_params.to_s
                  - data_attr[:key] = "#{id_filter['key']}[]"
                  - if id_filter['name'].match(/color/i).present?
                    - color_val = (list['color_code'] == "-1") ? "null" : "rgba(#{list['color_code']})"
                    .single_line_div
                      - if params['facet'].present? && params['facet'][data_attr[:property_key]].present? && params['facet'][data_attr[:property_key]]['value'].include?(list['value']) && is_mobile_view?
                        - data_attr[:chip] = "#{id_filter['name']} - #{list['name']}"
                        %a.facet-link{href: link}
                          .on-off-checkbox.color-switch
                            %input.filter-select.color-input{name: "#{id_filter['key']}-#{id_filter['name']}[]" ,id: "#{id_filter['key']}_#{list['value']}" ,type: 'checkbox', placeholder: "#{list['name']}".downcase.tr(' ','-'), value: "#{list['value']}", checked: 'checked', class: [('color-append' if ['saree-color','kameez-color','color', 'lehenga-color'].include?(id_filter['name'].parameterize)),("#{id_filter['name'].parameterize}")], data: data_attr }
                            %label.label-custom-color{for: "#{id_filter['key']}_#{list['value']}", class: ('multicolor-value' if list['color_code'] == "-1"), style: "background: #{color_val}"}
                          %label.label-custom{for: "#{id_filter['key']}_#{list['value']}", class: ('label-desktop-fix' unless browser.device.mobile?)}
                            = list['name']
                            - if list['count'] > 0
                              %span.round.success.label= list['count']
                      - else
                        %a.facet-link{href: link}
                          .on-off-checkbox.color-switch
                            %input.filter-select.color-input{name: "#{id_filter['key']}-#{id_filter['name']}[]" ,id: "#{id_filter['key']}_#{list['value']}" ,type: 'checkbox', placeholder: "#{list['name']}".downcase.tr(' ','-'), data: data_attr, value: "#{list['value']}", class: [("color-append" if ['saree-color','kameez-color','color', 'lehenga-color'].include?(id_filter['name'].parameterize)),("#{id_filter['name'].parameterize}")]}
                            %label.label-custom-color{for: "#{id_filter['key']}_#{list['value']}", class: ('multicolor-value' if list['color_code'] == "-1"), style: "background: #{color_val}"}
                          %label.label-custom{for: "#{id_filter['key']}_#{list['value']}", class: ('label-desktop-fix' unless browser.device.mobile?)}
                            = list['name']
                            - if list['count'] > 0
                              %span.round.warning.label= list['count']
                  - else
                    - class_type = box_type == 'radio' ? 'on-off-radiobox' : 'on-off-checkbox'
                    .single_line_div{style: "margin-bottom: 0.5rem;"}
                      - if params['facet'].present? && params['facet'][data_attr[:property_key]].present? && params['facet'][data_attr[:property_key]]['value'].include?(list['value']) && is_mobile_view?
                        - data_attr[:chip] = "#{id_filter['name']} - #{list['name']}"
                        %a.facet-link{href: link}
                          .switch.tiny{class: class_type}
                            %input.filter-select{name: "#{id_filter['key']}-#{id_filter['name']}[]" ,id: "#{id_filter['key']}_#{list['value']}" ,class: "#{id_filter['name'].parameterize}" ,type: box_type, value: "#{list['value']}", checked: 'checked', data: data_attr}
                            %label{for: "#{id_filter['key']}_#{list['value']}"}
                          %label.label-custom{for: "#{id_filter['key']}_#{list['value']}", class: ('label-desktop-fix' unless browser.device.mobile?)}
                            = list['name']
                            - if list['count'] > 0
                              %span.round.success.label= list['count']
                      - else
                        %a.facet-link{href: link}
                          .switch.tiny{class: class_type}
                            %input.filter-select{name: "#{id_filter['key']}-#{id_filter['name']}[]" ,id: "#{id_filter['key']}_#{list['value']}" ,class: "#{id_filter['name'].parameterize}" ,type: box_type, value: "#{list['value']}", data: data_attr}
                            %label{for: "#{id_filter['key']}_#{list['value']}"}
                          %label.label-custom{for: "#{id_filter['key']}_#{list['value']}", class: ('label-desktop-fix' unless browser.device.mobile?)}
                            = list['name']
                            - if list['count'] > 0
                              %span.round.warning.label= list['count']
    .short_filter_btn{class: ('filter-desktop-fix' unless (browser.device.mobile? || browser.device.tablet?))}
      #filter-apply-btn.btn_apply.button.small-4.columns Apply
      #filter-clear-btn.btn_clear.button.small-4.columns Clear All
      #filter-modal-close.btn_close.button.small-4.columns Close
  - category_name = facets_info['name'] if facets_info
  #facet-data{style: 'visibility: hidden;', data: { url: page_base_url, category_name: category_name } }
    - if @facet_properties.present?
      - hidden_property_values = @facet_properties.select { |prop_value| prop_value if prop_value[:priority] < 0 }
      - hidden_property_values.each do |value|
        - value[:priority] = value[:priority].abs
        .hidden-facet-data{data: value}