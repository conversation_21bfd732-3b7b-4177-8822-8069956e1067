- index = request.path.index(params[:facets]) - 2 if params[:facets].present?
- page_base_url = index ? request.path[0..index] : request.path
- facets_info = FACETED_URL_KINDS[params[:kind]]
- if facets_info
  - url_params = create_url_parameters(@facet_properties, params)
#designable_details.row.panel_block
  -if params[:max_odr].present?
    .span#max-odr-parameter{data: {max_odr: params[:max_odr]}}
  - if @store_page['filters']['list'].present?
    // array for displaying only important filters
    - rating_price = RATING_ENABLE ? ['rating','price','discount percent'] : ['price','discount percent']
    .columns.panel_heading.chips_maker
    .columns.small-4.panel_content
      %header
        %nav
          - @store_page['filters']['list'].each.with_index(1) do |filter, index|
            - if is_mobile_view?
              - if filter['type'] == 'id_filters'
                = render partial: 'store/mweb_filters/id_filter', locals: {id_filter: filter, page_base_url: page_base_url, url_params: url_params,facets_info: facets_info, index: index}
              - elsif filter['type'] == 'range_filter' && is_mobile_view?
                = render partial: 'store/mweb_filters/range_filter', locals: {range_filter: filter, rating_price: rating_price,  page_base_url: page_base_url, url_params: url_params,facets_info: facets_info, range_index: index}
              - elsif filter['type'] == 'gender_filter'
                = render partial: 'store/mweb_filters/gender_filter', locals: {gender_filter: filter,  page_base_url: page_base_url, url_params: url_params,facets_info: facets_info, index: index}
    .columns.small-8.panel_content.tabs-content
      - @store_page['filters']['list'].each.with_index(1) do |filter, index|
        - if is_mobile_view?
          - if filter['type'] == 'id_filters'
            = render partial: 'store/mweb_filters/id_content', locals: {id_filter: filter, page_base_url: page_base_url, url_params: url_params,facets_info: facets_info, index: index}
          - elsif filter['type'] == 'range_filter'
            = render partial: 'store/mweb_filters/range_content', locals: {range_filter: filter, rating_price: rating_price,  page_base_url: page_base_url, url_params: url_params,facets_info: facets_info, range_index: index}
          - elsif filter['type'] == 'gender_filter'
            = render partial: 'store/mweb_filters/gender_content', locals: {gender_filter: filter,  page_base_url: page_base_url, url_params: url_params,facets_info: facets_info, index: index}
    .short_filter_btn{class: ('filter-desktop-fix' unless (browser.device.mobile? || browser.device.tablet?))}
      #filter-apply-btn.btn_apply.button.small-4.columns Apply
      #filter-clear-btn.btn_clear.button.small-4.columns Clear All
      #filter-modal-close.btn_close.button.small-4.columns Close
  - category_name = facets_info['name'] if facets_info
  #facet-data{style: 'visibility: hidden;', data: { url: page_base_url, category_name: category_name } }
    - if @facet_properties.present?
      - hidden_property_values = @facet_properties.select { |prop_value| prop_value if prop_value[:priority] < 0 }
      - hidden_property_values.each do |value|
        - value[:priority] = value[:priority].abs
        .hidden-facet-data{data: value}