=render partial: '/layouts/initialise_grading'
.page-delimeters{style: "float:left;", data: {page_number: (params[:page] || 1)}}
- market_rate = CurrencyConvert.countries_marketrate[@country_code] || 1
- category = []
- if @category.present? || @canonical_path.present?
  - main_category = @category.present? ? @category.title : ""
  - main_category_id = @category.present? ? @category.id : "" 
  - if @category.present?
    - category = @category.breadcrumb_path.keys
    - array_index = category.length > 2 ? 2 : 1
  - if @canonical_path.present?
    - category[1] = "Designer"
    - array_index = 1
  - title = category[array_index]
  - item_listing_name, item_listing_id = [title, Category.find_by("LOWER(title) = '#{title.try(:downcase)}'").try(:id)] if title.present? && @category.present?
- bmgn_promotion =  Promotion.bmgnx_offer_message
- currency_symbol = get_symbol_from(@hex_symbol)
- store_designs.each_with_index do |design, index|
  - indexplusone = index + 1 # We need a 1 indexed array duh!
  - if design['designer_discount_end_time'].present? && Time.zone.now < design['designer_discount_end_time'] && !(opera_mini_browser? || uc_browser?)
    - end_date = design['designer_discount_end_time']
  - if design['sizes'] && design['sizes']['long_webp']
    - image_src = design['sizes']['long_webp']
  - elsif design['sizes']
    - image_src = design['sizes']['large']
  - else
    - image_src = '//#{asset_path("default_image.jpg")}'
  - image_src = "#{asset_path("default_image.jpg")}" if design['sizes'].empty?
  - inr_discount_price = (design['discount_price'] * market_rate).round(CurrencyConvert.round_to)
  - main_price = (design['price'] * market_rate).round(CurrencyConvert.round_to)
  - discount = (main_price - inr_discount_price).round(CurrencyConvert.round_to)
  - ga_data = {item_id: design['id'].to_s, item_name: "#{design['title']}", price: inr_discount_price, discount: discount || 0, index: indexplusone, item_category: category[1] || "", item_category2: category[2] || "", item_category3: category[3] || "", item_category4: category[4] || "", item_category5: category[5] || "", item_variant: design['color'] || "" , list: @ga_list, item_brand: design['brand'], item_list_name: item_listing_name || "", item_list_id: item_listing_id.to_s || "", country_code: @country_code, main_category: main_category || "", main_category_id: main_category_id.to_s || ""}.to_json.html_safe
  
  - if @banner_category.present?
    - banners = @banner_category[index]
    - if banners.present?
      = render_banner_slider(banners: banners, category_name: @category.name)

  %li.ga_design_container{unbxdattr: 'product', unbxdparam_sku: "#{design['id']}", :itemprop => "itemListElement", :itemscope => "", :itemtype => "http://schema.org/Product", data: {ga_data: ga_data, ga_pending: true}}
    - if design['product_offer'].present?
      - if design['product_offer']['type'] == 'flash_deals'
        - offer_message =  "Flash Deals"
      -elsif design['product_offer']['type'] =='bmgnx'
        - offer_message = bmgn_promotion
    -elsif end_date.present?
      .designer_timers{data:{countdown: end_date}}
    -if design.present? && eligible_for_free_shipping(design["discount_price"]) && product_offer_free_shippable?(design['product_offer']) && !(design['categories'].map { |category| category['id'] } & EXCLUDE_FREE_SHIPPING_CATEGORIES).present?
      - offer_message = "Free Shipping"
    %div.fr_page
      - link_attributes = {href: design['design_path'],unbxdattr: 'product',unbxdparam_sku: design['id'],unbxdparam_requestId: session[:unbxdparam_requestId],data: {:turbolinks=> false, unbxdattr: 'product',  unbxdparam_sku: design['id']}}

      - if !is_mobile_view?
        - link_attributes[:target] = '_blank'

      %a.catalog_product.design-detail-link{ link_attributes }
        - if design['ready_to_ship']
          .rts_plp_logo
        - elsif design['discount_percent'] > 0 && cookies[:theme] == 'red_theme' && design['designer'] != "Manyavar"
          -# .details_block.discount_new_block
          -#   = "#{design['discount_percent']}" + '% OFF'

        -# - if design['mirraw_certified'] == true
        -#   %img.catalog-cert{src: "#{asset_path('mirraw-cert1.jpg')}", style: ("margin-top: 5px;" if end_date.present?)}
        %span{:itemprop => "image", :content => IMAGE_PROTOCOL + image_src}
        .plp-image-box
          %div{class: "design_#{(index+1)%9} img_placeholder"}
            - if indexplusone <= 4 || (!LAZY_IMAGE_SCROLL && params[:more_designs].present?)
              %img.error_img{alt: "#{design['title']}", src: IMAGE_PROTOCOL + image_src, width: "204", height: "307"}
            - elsif LAZY_IMAGE_SCROLL
              %img.error_img.js-lazy{alt: "#{design['title']}", src: BASE_64_PLACHOLDER_IMAGE, data: {original: IMAGE_PROTOCOL + image_src}, width: "204", height: "307"}
            - else
              %img.error_img.lazy{alt: "#{design['title']}", src: BASE_64_PLACHOLDER_IMAGE, data: {src: IMAGE_PROTOCOL + image_src}, width: "204", height: "307"}
        - if RATING_ENABLE  
          -average_rating = @country_code == 'IN' ? design['average_rating_domestic'].to_f : design['average_rating_international'].to_f
          -total_reviews = design['all_reviews'].count
          -color_condition = {one: average_rating < 2.0, two: (average_rating >= 2.0 && average_rating < 3.5), three:  average_rating >= 3.5}
          .rating_div.catalog-rating
            -if (average_rating > 1.0)
              %span.small_rating
                =average_rating.round(1).to_s
                %span{class: [('red-rating' if color_condition[:one]), ('orange-rating' if color_condition[:two]), ('green-rating' if color_condition[:three])]} ★
                -if total_reviews > 0
                  | #{total_reviews}
        - if design['discount_percent'] > 0 && cookies[:theme] != 'red_theme'
          .details_block.discount_new_block
            = "#{design['discount_percent']}" + '% OFF'

        
      .panel.design_desc
        -# .row.truncate.designer_name
        -#   = design['designer'].titleize
        .row
          .small-12.columns.title-column.title-container
            - link_attributes_title = {:href => design['design_path'], unbxdparam_requestId: session[:unbxdparam_requestId],  unbxdattr: 'product', unbxdparam_sku: "#{design['id']}", :data => {:turbolinks => false, :unbxdattr => 'product', :unbxdparam_sku => "#{design['id']}", :unbxdparam_prank => "#{indexplusone}"}}
            - if !is_mobile_view?
              - link_attributes_title[:target] = '_blank'
            %a.design-detail-link{ link_attributes_title }
              %span.truncate-new{:itemprop => "name"}
                = design['title'].titleize
              %br
            .small-2.columns.wishlist-column
            =render partial: '/wishlists/wishlist_forms', locals: {design: design}
        .row.price-box
          .col-sm-12.details_block.discount-price.price-padding          
            #{get_symbol_from(@hex_symbol)}#{number_with_delimiter(design['discount_price'])}
          .col-sm-12.details_block.price-padding
            - if design['discount_percent'] > 0 && design['designer'] != "Manyavar"
              .actual_price #{get_symbol_from(@hex_symbol)}#{design['price']} 
          .col-sm-12.details_block.discount-block.price-padding
            - if design['discount_percent'] > 0
              =  "#{design['discount_percent'].to_i}" + '% OFF'
          
          
        .row
          .small-12.columns.title-column.offer-message-frame
            - custom_tags = design['custom_tags'] || []
            -if design['ready_to_ship'] && offer_message.present?
              %span
                Ready to Ship
              %br
              %span
                = offer_message
              %br
            - elsif design['ready_to_ship']     
              %span
                Ready to Ship
              %br
              - if custom_tags.any?
                %span.tag= custom_tags.second || custom_tags.first
                %br
            - elsif offer_message.present?
              %span= offer_message
              %br
              - if custom_tags.any?
                %span.tag= custom_tags.second || custom_tags.first
                %br
            - elsif custom_tags.any?
              %span.tag= custom_tags.first
              %br
              - if custom_tags.second.present?
                %span.tag= custom_tags.second
            - if design['boosted']
              %span.tag= "Ad"
        - if false #is_domestic?
          - if design['state'] == 'in_stock'
            %div.add_to_cart_link.button.tiny.add_new_pos.success{id: design['id'], unbxdattr: "AddToCart", unbxdparam_sku: "#{design['id']}", unbxdparam_requestId: session[:unbxdparam_requestId], data: {id: "#{design['id']}", category: "#{design['category_name']}", brand: "#{design['brand']}", price: "#{design['inr_discount_price']}", quantity: "#{design['stock']}", gradingExperimentId: @current_experiment.to_s}}
              = 'ADD TO CART'
          - else
            %div.sold_out_link.button.disabled.tiny.alert.sold-out-btn.sold-out
              = 'Sold Out'
    %span{:itemprop => "offers", :itemscope => "", :itemtype => "http://schema.org/Offer"}
      %span{:itemprop => "url", :content => design['design_path']}
      %span{:itemprop => "priceCurrency", :content => schema_symbol(@symbol)}
      %span{:itemprop => "price", :content => design['discount_price']}
      - if design['state'] == 'in_stock'
        %span{:itemprop => "availability", :content => "http://schema.org/InStock", :itemtype => "http://schema.org/ItemAvailability"}
      - else
        %span{:itemprop => "availability", :content => "http://schema.org/OutOfStock", :itemtype => "http://schema.org/ItemAvailability"}


:javascript
  afterWindowOrTrubolinksLoad(function(){
      MR.blazeSlider.init()
    })
  
