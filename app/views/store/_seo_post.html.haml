-if render_seo_content? && @seo.post?
  .hidden-xs.footer-info
    - if @store_page.present?
      - designs = @store_page['designs'].to_a.first(10)
    - elsif @bestseller_designs.present?
      - designs = @bestseller_designs.to_a.first(10)
    .seo-text-box
      =raw @seo.post.gsub('<linebreak>','').gsub('<br>', '')
    - if designs.present?
      .price-list-wrapper#price_box
        %h2 Latest #{@seo.label.titleize} Collection with Price
        %table
          %thead
            %tr
              %th Sr. No.
              %th.title-header #{@seo.label.titleize} List
              %th Price (#{get_symbol_from(@hex_symbol)})
          %tbody
            - counter = 0
            - designs.each do |design|
              - next unless design['designer'].present?
              - counter += 1
              %tr
                %td #{counter}.
                %td
                  %a{:href => design['design_path']}
                    =design['title']
                %td= get_price_with_symbol(design['discount_price'], @hex_symbol)
        -last_updated_at = Time.now.in_time_zone('Eastern Time (US & Canada)')
        .updated-date Last updated on #{last_updated_at.strftime("%m/%d/%Y")}
-# - if @seo.present? && @seo.post? && !(params[:facets].try(:index, 'colour-') == 0 && @seo.label == params[:kind].try(:downcase))
-#   .footer-info
-#     #seo_post=raw @seo.post
    -#%center
    -#%a#view-more-seo-post.show Read More