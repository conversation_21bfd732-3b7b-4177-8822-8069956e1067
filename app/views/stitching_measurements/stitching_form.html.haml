- content_for :page_specific_css do
  = stylesheet_link_tag 'stitching_measurement'
  = stylesheet_link_tag 'pnotify.custom.min'
- content_for :page_specific_js do
  =javascript_include_tag 'stitching_measurement','pnotify.custom.min', 'stitching_video'
- order_number = @order_number
- item_id = @line_item_id
- user_id = @user_id
- source = params[:source].to_s == 'email' ? 'Email' : 'Order_page'
- app_source = @app_source
-# %script{:type => 'text/javascript'}
  ga('send', 'event', 'StitchingForm', 'Open', 'order_#{order_number}/item_#{item_id}/user_#{user_id}/source_#{source}/app_source_#{app_source}');
%meta{content:'width=device-width, initial-scale=1.0, maximum-scale=6.0, user-scalable=1', name: 'viewport'}
-if @stitching_measurements.size >= @quantity
  -@stitching_measurements.each_with_index do |stitching_measurement,index|
    %table.columns.small-12
      %tr
        %td{colspan: '2'}
          .alert-box.success Measurement #{index + 1}
      =render partial: 'stitching_data_view',locals: {stitching_measurement: stitching_measurement}
-else
  -unless current_account.present?
    .reveal-modal{id: "login_modal" ,"aria-hidden" => "true", "aria-labelledby" => "modalTitle", "data-reveal" => "", :role => "dialog",style:'min-height: 52vh'}
      %a.close-reveal-modal{"aria-label" => "Close", href: "#", role: "button"} ×
      #details{style: 'color:black;'}
        %br
        %br
        Login to use your previously saved measurements
        =link_to 'Login',new_account_session_url(protocol: Rails.application.config.partial_protocol),class: 'button info', style: 'color:white !important;background-color: #007095 !important;width:100%;'
        %br
        %hr.login_hr
        %br
        #close_modal.button.warning{style: 'width:100%;'} Continue without Login
  .youtube_measurement_button 
    %ul
      %li
        %a{class: 'help_link measurement_button', "data-reveal-id" => "measurement_modal", "data-video-src" => "https://www.youtube.com/embed/hEcXOfXMncU?si=7bgnF7z6mG0JcGtK", :href => "#"}
          %h5 How to take upper body measurement?
          %svg.bi.bi-youtube{fill: "currentColor", height: "16", style: "color: red", viewbox: "0 0 16 16", width: "16", xmlns: "http://www.w3.org/2000/svg"}
            %path{d: "M8.051 1.999h.089c.822.003 4.987.033 6.11.335a2.01 2.01 0 0 1 1.415 1.42c.101.38.172.883.22 1.402l.01.104.022.26.008.104c.065.914.073 1.77.074 1.957v.075c-.001.194-.01 1.108-.082 2.06l-.008.105-.009.104c-.05.572-.124 1.14-.235 1.558a2.007 2.007 0 0 1-1.415 1.42c-1.16.312-5.569.334-6.18.335h-.142c-.309 0-1.587-.006-2.927-.052l-.17-.006-.087-.004-.171-.007-.171-.007c-1.11-.049-2.167-.128-2.654-.26a2.007 2.007 0 0 1-1.415-1.419c-.111-.417-.185-.986-.235-1.558L.09 9.82l-.008-.104A31.4 31.4 0 0 1 0 7.68v-.123c.002-.215.01-.958.064-1.778l.007-.103.003-.052.008-.104.022-.26.01-.104c.048-.519.119-1.023.22-1.402a2.007 2.007 0 0 1 1.415-1.42c.487-.13 1.544-.21 2.654-.26l.17-.007.172-.006.086-.003.171-.007A99.788 99.788 0 0 1 7.858 2h.193zM6.4 5.209v4.818l4.157-2.408L6.4 5.209z", fill: "red"}
      %li
        %a{class: 'help_link measurement_button', "data-reveal-id" => "measurement_modal", "data-video-src" => "https://www.youtube.com/embed/jE1YXYo1EHw?si=sYQgYbU-E4lJM_4e", :href => "#"}
          %h5 How to take lower body measurement?
          %svg.bi.bi-youtube{fill: "currentColor", height: "16", style: "color: red", viewbox: "0 0 16 16", width: "16", xmlns: "http://www.w3.org/2000/svg"}
            %path{d: "M8.051 1.999h.089c.822.003 4.987.033 6.11.335a2.01 2.01 0 0 1 1.415 1.42c.101.38.172.883.22 1.402l.01.104.022.26.008.104c.065.914.073 1.77.074 1.957v.075c-.001.194-.01 1.108-.082 2.06l-.008.105-.009.104c-.05.572-.124 1.14-.235 1.558a2.007 2.007 0 0 1-1.415 1.42c-1.16.312-5.569.334-6.18.335h-.142c-.309 0-1.587-.006-2.927-.052l-.17-.006-.087-.004-.171-.007-.171-.007c-1.11-.049-2.167-.128-2.654-.26a2.007 2.007 0 0 1-1.415-1.419c-.111-.417-.185-.986-.235-1.558L.09 9.82l-.008-.104A31.4 31.4 0 0 1 0 7.68v-.123c.002-.215.01-.958.064-1.778l.007-.103.003-.052.008-.104.022-.26.01-.104c.048-.519.119-1.023.22-1.402a2.007 2.007 0 0 1 1.415-1.42c.487-.13 1.544-.21 2.654-.26l.17-.007.172-.006.086-.003.171-.007A99.788 99.788 0 0 1 7.858 2h.193zM6.4 5.209v4.818l4.157-2.408L6.4 5.209z", fill: "red"}

  .reveal-modal.stitching_help_video{id: "measurement_modal", "aria-hidden" => "true", "aria-labelledby" => "modalTitle", "data-reveal" => "", :role => "dialog"}
    %a.close-reveal-modal{"aria-label" => "Close", href: "#", role: "button"} ×
    #details{style: 'color:#3C4345;'}
      %iframe#video_frame{width: "560", height: "315", frameborder: "0", allow: "accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture", allowfullscreen: true}

  %form.measurement-experience
    %h4 How good are you at measuring?

    %ul.measurement-experience-options
      %li
        = radio_button_tag :measurement_experience_options, 1
        = label_tag :measurement_experience_options_1, 'Noobie'
      %li
        = radio_button_tag :measurement_experience_options, 2
        = label_tag :measurement_experience_options_2, 'Experienced'
      %li
        = radio_button_tag :measurement_experience_options, 3
        = label_tag :measurement_experience_options_3, 'Professional'

    %dl.measurement-experience-options-explanation
      %dt Noobie
      %dd
        %ul
          %li I do not know how to measure and need complete guidance for the same.
          %li
            I want Mirraw to predict the remaining measurements based on the key measurements
            I provide.
          %li I want the styling of the garment to be the same as the image shown on the website.

        %em
          You'll only have to enter the key 4-5 measurements and the rest will be predicted by us.

      %dt Experienced
      %dd
        %ul
          %li I know how to measure but need some guidance for the same.
          %li
            I want Mirraw to predict the remaining measurements based on the key measurements
            I provide.
          %li I want to manually select the styling of the garment from the given available options.

        %em
          You'll only have to enter the key 4-5 measurements and the rest will be predicted by us.

      %dt Professional
      %dd
        %ul
          %li I know how to measure and do not need any guidance for the same.
          %li
            I have my measurements with me measured by a professional and want the garment to be
            stitched of the same size.
          %li I want to manually select the styling of the garment from the given available options.

    = submit_tag "Next", class: 'button small next-stage', disabled: true

  %ul.accordion{"data-accordion" => ""}
    %li.accordion-navigation.hide_on_review
      .disable_accordion Your Details
      #primary_form.content.active
        .conversion.measurement_type Mirraw recommends taking measurements with the assistance of someone
        -if @quantity > 1
          .alert-box.info Form No #{@stitching_measurements.size + 1}
        .form-group
          - if current_account.present?
            - if (user = current_account.try(:user)).try(:full_size_photo).present?
              .row
                .large-12.columns
                  %label Your Full Size Photo :
              %br
              .row
                .large-12.columns
                  = image_tag(user.full_size_photo.url(:zoom),id:'full_size_photo',class: 'img-thumbnail', style:'width:304; height:236;')
                  %br
                  %br
                  %label OR
            .row
              .large-12.columns
                %label Please Upload Your Full Size Photo :
                .conversion.measurement_type (Max Size: 4MB and PNG,JPG,JPEG Format Only)
            %br
            = form_tag upload_full_size_photo_path,multipart: true, method: 'POST',id:'upload-form',class: 'form-inline' do
              = hidden_field_tag 'user_id',current_account.try(:user).try(:id)
              .row
                .large-12.columns
                  = file_field_tag :photo, required: true, accept: '.jpeg,.jpg,.png', id: 'upload-file', class: 'form-control'
              .row
                .small-offset-3.small-6.columns
                  = submit_tag 'Upload Photo',id:'upload-submit',class: 'button round success small',style:'width:35%,text-align:center;margin-top:20px;'
            %hr

        =form_tag nil,id: 'height_weight_form',class: 'form-group',id: 'measurement_data_1' do
          =hidden_field_tag :design_id,@design.id
          =hidden_field_tag :product_designable_type, params[:product_designable_type]
          =hidden_field_tag :height,4.0
          =hidden_field_tag :order_id,@order_id
          =hidden_field_tag :design_id,@design.id
          =hidden_field_tag :quantity,@quantity
          =hidden_field_tag :line_item_id,@line_item_id
          = hidden_field_tag :measurement_experience
          =hidden_field_tag :order_number, order_number
          =hidden_field_tag :item_id, item_id
          =hidden_field_tag :user_id, user_id
          =hidden_field_tag :source, source
          =hidden_field_tag :app_source, app_source
          =hidden_field_tag :plus_size_addon, @plus_size_product
          -if current_account.present? && current_account.user.id == @user_id
            .row
              .small-12.columns
                .label
                  Measurement Name
                  =select_tag :measurement_select,options_for_select(@user_measurements)
                .conversion.measurement_type Create New Measurements Or Use Your Previous Measurements
          .row
            .small-12.columns
              %hr
              - if params[:product_designable_type] != 'lehenga_choli' 
                - body_mes_info = 'Garment is made 2-3 inch loose in case actual body measurement is filled.'
              - else 
                - body_mes_info = 'Submit your body measurements and our tailor will suitably stitch the garment as per tailoring principles'
              =radio_button("measurement_type", "garments", "Body Measurement",checked: true,class: 'form primary_form_input measurement_type_radio',data: {content_id: '#body_measurement'})
              %label{for: 'measurement_type_garments_body_measurement'} Body Measurement
              .conversion.measurement_type= body_mes_info
              %hr
              - if params[:product_designable_type] != 'lehenga_choli'
                =radio_button("measurement_type", "garments", "Garment Measurement",class: 'form primary_form_input measurement_type_radio',data: {content_id: '#garment_measurement'})
                %label{for: 'measurement_type_garments_garment_measurement'} Garment Measurement
                .conversion.measurement_type Garment will be made in the same measurement as filled, no extra loosing will be added.
                %hr
          .row
            .large-12.columns
              .label
                Enter Height
          .row
            .small-4.columns
              .label
                Feet
                = select_tag :feet,options_for_select((4..7).to_a)
            .small-4.columns
              .label
                Inches
                = select_tag :inches,options_for_select((0..11).to_a),prompt:'Select',required:true
            .small-4.columns
          .row
            .small-12.columns
              .label#conversion_height.conversion
          .row
            .small-12.columns
              .label
                Enter Weight
                = number_field_tag :weight,nil,placeholder: 'In Kgs',max: 200,min: 20,class: 'small-12',required: true
            .small-12.columns
              .label#conversion_weight.conversion
          .row
            .small-12.columns
              .label
                Enter Age
                = number_field_tag :age,nil,placeholder: 'Yrs',max: 100,min: 1,class: 'small-12',required: true
          .row
            .small-offset-3.small-6.columns
              =submit_tag :submit_height_weight,value: 'Next Stage',class: 'next-stage button success small'
          .row
        .row
          -case @design.designable_type
          -when 'Lehenga'
            =image_tag('stitching/lehenga_size.jpg')
          -when 'Saree'
            =image_tag('stitching/blouse_size.jpg')
          -when 'Kurti'
            =image_tag('stitching/kurti.jpg')    
          -else
            =image_tag('stitching/salwar_size.jpg')
    %li#update_form_stitching
%img#loadingImage{ src: "#{asset_path('loading.gif')}",style: 'display:none;' }
