= javascript_include_tag 'selectize'

- content_for :page_specific_css do
  = stylesheet_link_tag 'orders'
=render partial: '/layouts/initialise_grading'
- if @order
  - currency_details = currency_params(@order)

  - if @track && @order.app_source.downcase != 'desktop'
    - fb_design_id = []
    %script{type: 'text/javascript'}
      window.addEventListener('load', function(){
      - @order.line_items.each do |item|
        - fb_design_id.push(item.design_id)
        unbxdTrack("order",{"pid":"#{item.design_id}","qty":"#{item.quantity}","price":"#{item.snapshot_price_currency(1)}"});
        Unbxd.track("order", {"pid": "#{item.design_id}", "qty": "#{item.quantity}", "price": "#{item.snapshot_price_currency(1)}"}})
      });
    -# :javascript
      addEvent(window, 'load', function(){
        if (typeof fbq !== 'undefined'){
          fbq('track', 'Purchase', {value: '#{@total}', currency: 'INR', content_ids: #{fb_design_id}, content_type : 'product', phone: '#{@phone_number}', city: '#{@city}', country: '#{@country}'.toLowerCase(), email: '#{@email}'});
        }
      });
      if(typeof ga != 'undefined'){
          gaOrder(#{@ga_hash.to_json});
      }

    = render partial: 'orders/adwords_conversion', locals: {adwords_total: @adwords_total}

  - if is_android?
    .row#get_app_link
      .panel.info.radius
        %h1
          GET THE APP NOW!
          %span.close#close_link &times;
        %p 
          Get realtime updates on your order, Live chat with customer support & avail app-only offers
        %a.success{:href => "https://play.google.com/store/apps/details?id=com.mirraw.android&utm_source=download_app_order_Ack&utm_medium=mirraw_mobile_web", :target => "_blank", rel: "noopener"} 
          Download Now &nbsp;
          %i.fi-social-android
  - elsif browser.platform.ios?
    .row#get_app_link
      .panel.info.radius
        %h1
          GET THE APP NOW!
          %span.close#close_link &times;
        %p 
          Get realtime updates on your order, Live chat with customer support & avail app-only offers
        %a.success{:href => "https://itunes.apple.com/app/apple-store/id1112569519?pt=118123691&ct=mobile_orderAck_Button&mt=8", :target => "_blank", rel: "noopener"} 
          Download Now &nbsp;
          %i.fi-social-apple

  .row#order_show_block
    .large-12.medium-12.small-12.columns.small-centered
      %h4.text-center
        %div.order-ack-title Order Acknowledgement

      - if @order.satisfies_cashback_conditions? && @order.cashback_available? && !@order.cashback_rewarded?
        %p.row.cashback-reminder
          %strong= t('cashback.reminder.html', price: "#{@order.currency_code} #{get_price_in_currency(@order.other_details['loyalty_rewards_credit'].to_f, @order.currency_rate)}")

      .row
        .columns.bordered_block
          - ordered_on_text = "Ordered On : #{@order.created_at.in_time_zone.strftime('%d %b %Y')}"
          .row
            .columns
              .left= "Order No : #{@order.number}"
              - if @order.expected_delivery_date.present?
                .show-for-medium-up.right= "Expected Delivery : #{@order.expected_delivery_date.strftime('%d %b %Y')}"
              .text-center
                =ordered_on_text
          .row
            .columns
              .show-for-small-only= ordered_on_text
      -show_domestic_pipeline = true
      - if @order.international? || @all_line_items.map(&:stitching_required).include?('Y')
        = render :partial => '/orders/order_status_pipeline'
        -show_domestic_pipeline = false
      = render partial: 'orders/order_acknowledgement_address'
      = render partial: 'orders/summary', locals: {currency_details: currency_details}
      = render partial: 'orders/designer_orders', locals: {currency_details: currency_details, retry_state: @retry_state,show_domestic_pipeline: show_domestic_pipeline}
      -if @order.cancellable?
        #cancel_panel.row.text-center
          %a.button.tiny.cancel_button{data: {'reveal-id' => 'cancel-confirmation-modal'}} Cancel Order
          #cancel-confirmation-modal.reveal-modal{data: {reveal: ''}, aria: {hidden: true, labelledby: 'cancel-confirmation-modal-title'}, role: 'dialog'}
            %h4#cancel-confirmation-modal-title Do you really want to cancel this order?

            %p.cancel-reason-request
              %strong Please provide a reason for cancellation.
            %ul.selectize
              - t('order.cancellation.reasons').each_with_index do |reason, index|
                %li.selectize-option
                  = radio_button_tag "cancellation-reason-option", reason
                  = label_tag "cancellation-reason-option_#{reason}", reason
              %li.selectize-option.selectize-option-last
                = radio_button_tag "cancellation-reason-option", "Other"
                = label_tag "cancellation-reason-option_Other", "Other", class: 'selectize-other-option-toggle'
                = text_field_tag "cancellation-reason-other-input", '', class: 'selectize-other-option-toggle is-hidden'
            %div.cancel-order-reason-select-warning.is-hidden
              %em Please select one of the reasons for cancelling.

            / %p.cancel-helper-info
            /   %em If you select "Yes", then an OTP will be sent to your mobile number which you will have to enter in the next step.
            %hr
            %a.success.button.yes_button Yes 
            %a.alert.button.no_button{aria: {label: 'Close'}} No
            %a.close-reveal-modal{aria: {label: 'Close'}} &#215;

          #otp-verification-modal.reveal-modal{data: {reveal: ''}, aria: {hidden: true, labelledby: 'otp-verification-modal-title'}, role: 'dialog'}
            %a.close-reveal-modal{aria: {label: 'Close'}} &#215;
            #otp-form-and-content
              Enter verifcation code that has been sent to following number
              to confirm the cancellation of the order
              #otp-show-phone-details.row{type: 'text', value: @order.billing_phone, readonly: true}
                = @order.billing_phone
              #otp-error-msg.error-msg-for-otp
                *Incorrect OTP Please try again.
              %form#otp-form
                = text_field_tag 'cod-otp', '', class: 'cod-otp-input', autocomplete: 'off', required: true, type: 'tel', maxlength: '5'
                .row
                  = submit_tag 'Confirm', id: 'otp-submit-button', class: 'button radius success cod-otp-modal-buttons'
                .otp-resend-message.text
                  .row
                    Didn't receive verification code yet?
                    %button#resend-otp.button-as-text{type: 'button'}
                      %span Resend
      - elsif ['cancel','new','pending','confirmed','cancel_complete'].exclude? @order.state
        #return_panel.row.text-center
          .columns.bordered_block
            .row
              -if current_account.present? && current_account.user?
                %button.button.tiny.policy_button{"data-reveal-id" => "return_policy"} Return
                #return_policy.reveal-modal{data: {reveal:"", v_offset: "0"}, style: 'padding: 0px;'}
                  .modal-dialog.modal-sm-size
                    .modal-content{style: 'padding:4%;font-size: 12px;'}
                      %a{class:"close-reveal-modal button",'aria-label'=> 'Close', style: 'float: right; padding: 0px 10px; font-size: 35px; border-radius: 50%;'} &#215
                      .modal-header
                        %h6.modal-title{style:'text-decoration:underline;'} Return Policy
                      .modal-body
                        = render :partial => '/orders/return_policy'
                      .modal-footer{style: 'text-align:center;'}
                        =link_to 'I Agree', user_order_return_items_path(current_account.try(:user),order_number: @order.number),class: 'button tiny', data: {turbolinks: 'false'}
              -else
                =link_to 'Return', new_account_session_url(protocol: Rails.application.config.partial_protocol), class: 'button tiny radius', style: 'background-color: #670b19;', data: {turbolinks: 'false'}
                
  -# - if @newly_added_products.present? and is_domestic?
  -#   =render partial: 'pages/newly_added_products', locals: {designs: @newly_added_products}   #commented out as we dont need newly added products as of now

  -# - if @track && JABBER_REVIEW_ENABLED  
    #InstantFeedbackWidget
      #InstantFeedbackWidgetInner.stjr-widget
    
    :javascript
      (function (src, callback) {

        var s, r, t; r = false;
        s = document.createElement('script');
        s.type = 'text/javascript';
        s.src = src;
        s.onload = s.onreadystatechange = function () {
          if (!r && (!this.readyState || this.readyState == 'complete')) {
            r = true; 
            callback();
          }
        };

        t = document.getElementsByTagName('script')[0];
        t.parentNode.insertBefore(s, t);
      }
      ('https://biz.sitejabber.com/js/widget.1573845672.js', function () {

        new STJR.InstantFeedbackWidget(
          {
            id: 'InstantFeedbackWidget',
            url: 'mirraw.com',
            user: {
              first_name: "#{@order.name.split(' ')[0]}",
              last_name:  "#{@order.name.split(' ')[1]}"
            },
            order_id: "#{@order.number}"
          }).render();
      }));

:javascript

  function closeAddonForm(id){
    $('body').css('overflow', 'scroll');
    $('.fixed').css('display', 'block');
    $('#modal_'+id).css('display', 'none');
  }

  function loadAddonForm(event, id){
    event.preventDefault();
    var frametarget = $(this).attr('href');
    $('body').css('overflow', 'hidden');
    $('.fixed').css('display', 'none'); // ios fix
    $('#modal_'+id).css('display', 'block');
  }

  $(document).ready(function(){

    setTimeout(function(){
      $("#get_app_link").slideDown('slow');
    }, 500);
    
    $("#close_link").on('click', function(){
      $("#get_app_link").slideUp('slow');
    });
  });

  $(document).on('click','.policy_button',function(){
    $('body').addClass('modal-open');
  });

  $(document).on('click', '.close-reveal-modal', function(e) {
    return $('body').removeClass('modal-open');
  });

  $(document).on('click', '.circle_stage_domestic,.uc_base', function(){
    id  = $(this).attr('id')
    background =  id.split("_")[1]
    text_id = "#status_text_"+id.split("_")[2]
    if( background == 'yellowgreen')
      note_text = "Your order has been successfully " + id.split("_")[0] + "."
    else
      note_text = "Your Order Will Be " + id.split("_")[0] + " Shortly."
    if (!$(text_id).text().includes('Your order has been canceled'))
      $(text_id).text(note_text) ;
  });

  $(document).on('click', '#cancel-confirmation-modal .no_button', function(event) {
    event.preventDefault();
    $('#cancel-confirmation-modal').foundation('reveal', 'close');
  });

  let cancellationReason;
  $(function() {
    cancellationReason = new Selectize($('.selectize'));
    $('.cancel-order-reason-select-warning').hide();
  });

  $(document).on('click', '#cancel-confirmation-modal .yes_button', function(event) {
    event.preventDefault();

    if (cancellationReason.isValid()) {
      $.ajax({
        type: 'POST',
        url: '/user/cancel_order',
        data: {
          order_number: "#{@order.number}"
        },
        success: function() {
          $('#cancel-confirmation-modal').foundation('reveal', 'close');
        }
      });
    } else {
      $('.cancel-order-reason-select-warning').show();
    }
  });

  $(document).on('click', '#otp-verification-modal #resend-otp', function(event) {
    event.preventDefault();

    $('#otp-error-msg').css('display', 'none');
    $('.cod-otp-input').val('');

    $.ajax({
      type: 'POST',
      url: '/otp/deliver',
      data: {
        order_number: "#{@order.number}"
      },
      dataType: 'script'
    });
  });

  $(document).on('submit', '#otp-form', function(event) {
    event.preventDefault();

    $.ajax({
      type: 'POST',
      url: '/otp/verify',
      data: {
        order_number: "#{@order.number}",
        code: $('#cod-otp').val(),
        order_cancel_reason: cancellationReason.value()
      },
      dataType: 'script',
      error: function() {
        $('#otp-error-msg').css('display', 'inline');
      }
    });
  });
- purchase_event_trigger = trigger_ga4_purchase_event(@order)
- if purchase_event_trigger
  - coupon_discount_amount = @order.discount_currency(currency_details[:rate]) || 0
  - tax_amount = @order.total_tax_currency(currency_details[:rate]) || 0
  - gift_wrap = @order.order_addon.present? ? @order.order_addon.gift_wrap_price_currency(currency_details[:rate]) : 0
  - if (market_rate = CurrencyConvert.countries_marketrate[@country_code]).present?
    - gift_wrap = (gift_wrap *= (market_rate).round(CurrencyConvert.round_to)).round(CurrencyConvert.round_to)
    - coupon_discount_amount = (coupon_discount_amount *= (market_rate).round(CurrencyConvert.round_to)).round(CurrencyConvert.round_to)
    - tax_amount = (tax_amount *= (market_rate).round(CurrencyConvert.round_to)).round(CurrencyConvert.round_to)
  - percent_off = (coupon_discount_amount * 100.0 / @totalvalue)
  :javascript
    window.dataLayer = window.dataLayer || [];
    var gads_items_id = #{@googe_add_hash_new.to_json};
    var customer_details = {
      name: "#{@name}",
      email: "#{@email}",
      city: "#{@city}",
      country: "#{@country}",
      state: "#{@state}",
      zipcode: "#{@pincode}",
      phonenumber: "#{@phone_number}"
    }
    var ga4_purchase_params = #{@ga_hash_new.to_json}; 
    var customizationTotal = 0;
    ga4_purchase_params.gift_wrap = #{gift_wrap}
    ga4_purchase_params.payout = #{@payout}
    ga4_purchase_params.coupon_discount = #{coupon_discount_amount};
    ga4_purchase_params.tax = #{tax_amount};
    ga4_purchase_params.items.forEach(function (item) {
      customizationTotal += item.item_customization * item.quantity;
    });
    ga4_purchase_params.customization = customizationTotal
    ga4_purchase_params.value = (#{@totalvalue} + #{gift_wrap}) - #{coupon_discount_amount} + customizationTotal;
    ga4_purchase_params.item_ids = gads_items_id.item_ids
    dataLayer.push({ ecommerce: null });
    dataLayer.push({
      event: "ga4_purchase",
      ecommerce: ga4_purchase_params
    });