json.ratings @design.designer&.enable_reviews ? @rating : {5 => 0, 4 => 0, 3 => 0, 2 => 0, 1 => 0}
json.total_ratings @design.designer&.enable_reviews ? @rating.values.sum : 0
json.design_avg_rating @design.designer&.enable_reviews ? @design.average_rating : 0
json.designer_avg_rating @design.designer&.enable_reviews ? @design.designer.average_rating : 0
json.reviews_details do 
  json.partial! '/api/v1/reviews/reviews', locals: { reviews: @reviews, curr_user: @curr_user }
end