json.cache! ["api/v1/design/#{Design.country_code}/#{@symbol}/#{request.env['sent_from_web_mobile'] || request.headers["Sent-from-Web-Mobile"]}", design.variants], expires_in: API_CACHE_LIFESPAN.minutes do
  ActiveRecord::Associations::Preloader.new.preload(design.variants, [option_type_values: :option_type]) if design.variants.present? && !design.variants.first.option_type_values.loaded?
  json.variants design.stitched_variants do |variant|
    json.id variant.id
    json.quantity variant.quantity
    json.price variant.price_currency(@rate)
    json.discount_price variant.effective_price_currency(@rate)
    json.in_warehouse variant.sor_available?
    json.variant_estimated_delivery_days variant.sor_available? ? design.delivery_date_for_rts(@actual_country).round : @number_of_days
    json.option_type_values variant.option_type_values do |option_type_value|
      json.name option_type_value.p_name
      json.option_type option_type_value.option_type.name
    end
  end
  json.variant_size_chart design.dynamic_size_chart_hash
  unstitched_variant = request.env['sent_from_web_mobile'] || request.headers["Sent-from-Web-Mobile"] ? design.unstitched_variant : nil
  if unstitched_variant.present?
    json.unstitched_variant_id unstitched_variant.id
    json.unstitched_variant_quantity unstitched_variant.quantity
    json.unstitched_variant_price unstitched_variant.price_currency(@rate)
    json.unstitched_variant_discount_price unstitched_variant.effective_price_currency(@rate)
  end
end