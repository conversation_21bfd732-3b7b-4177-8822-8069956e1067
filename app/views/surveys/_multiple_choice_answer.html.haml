.nps_questions.row{data: {condition: question.survey_terminating_condition }}
  .questions.col-lg-6
    %li.survey-questions-answers
      =question.question
  .answer
    -question.survey_answer_options.sort_by(&:value).each do |option|
      %li.survey-questions-answers
        =radio_button_tag name, option.name, 0, class: 'sub_issue_quality', data: {terminator: option.value}
        %label{:for => "#{name}_#{option.name}"} &nbsp #{option.name.try(:titleize)}