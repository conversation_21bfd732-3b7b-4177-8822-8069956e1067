-color_condition = {one: average_rating < 2.0, two: (average_rating >= 2.0 && average_rating < 3.5), three:  average_rating >= 3.5}
- color_classes = ['#FF5252', '#64B5F6', '#81C784', '#FF8A65', '#90A4AE', '#AED581', '#4DD0E1', '#9575CD', '#8D6E63', '#757575']
- @reviews ||= reviews

.review-container#review_of_design
  .heading_review
    Ratings and Reviews
    - if RATING_ENABLE && @design.present? && @reviews.present? && review_count > 3
      .view_all
        = link_to 'View all', design_reviews_path(@design['id'])


  - total_ratings = all_reviews.count
  
  .rating-summary-container
    .rating-overview
      %h4 Overall Ratings
      .rating-summary
        %span.rating-number{class: [('red-rating' if color_condition[:one]), ('orange-rating' if color_condition[:two]), ('green-rating' if color_condition[:three])]} #{average_rating.round(1)} ★
        %span.total-reviews= "#{total_ratings} Ratings & #{review_count} Reviews"
  
    .ratings-list
      - (1..5).reverse_each do |star|
        - count = all_reviews.select { |review| review['rating'].to_i == star }.count
        - percentage = total_ratings.zero? ? 0 : (count.to_f / total_ratings * 100).round
        - bar_color = case star
          - when 5 then '#81C784' # Green
          - when 4 then '#81C784' # Light Blue
          - when 3 then '#FFB74D' # Orange
          - when 2 then '#FFD54F' # Yellow
          - else '#FF5252' # Red for 1 star
        .rating-row
          .star-label= "#{star} ★"
          .progress-bar-container
            .progress-bar{style: "width: #{percentage}%; background-color: #{bar_color} !important;"}
          .rating-count= count

  


  #product_reviews.tabbed-view.all-products
    - color_classes.shuffle!
    - @reviews.each_with_index do |review, index|
      - if defined? review_count
        - reviewers_name = review['reviewers_name']
        - user_image_url = review['user_image']
      - elsif (user = review.user).present?
        - reviewers_name = "#{user['first_name'].to_s} #{user['last_name'].to_s}"
        - user_image_url = user['image_url']
      .review-row
        .block{id: "block_#{review['id']}"}
          .user-sign{style: "background-color: #{color_classes[index]} !important;"}
            %span.user-name
              - if user_image_url.present?
                = image_tag "#{user_image_url}", class: 'user_image'
              - else
                = reviewers_name.present? ? reviewers_name[0] : 'G'
          .stars.margin-left-top
            .ratings-score.row
              .star.col-sm-12{dscore: review['rating'].to_i}
            .user-name-full.row
              .col-sm-12
                %p
                  = reviewers_name.present? ? reviewers_name : 'Guest Review'
            .rated-ago.row
              .col-sm-12
                %p
                  = review['time_ago'] || time_ago_in_words(review['updated_at']) + ' ago'
          .review-text.margin-left-top
            - if review['review'].to_s.length > 150 && !check_for_alternate_tab?
              %P
                - show_view_more = true
                = truncate(review['review'].to_s, length: 200)
            - else
              - show_view_more = false
              = review['review'].to_s
          - if show_view_more
            %a{"data-reveal-id" => "view_more_content_#{review['id']}", href: "#"}
              .toggle_scroll.view-more-btn
                Read More
            #view_more_content.reveal-modal.black-content.rating-review{id: "#{review['id']}", "aria-hidden" => "true", "aria-labelledby" => "modalTitle", "data-reveal" => "", role: "dialog"}
              %h4#modalTitle
                = reviewers_name.present? ? reviewers_name : 'Guest Review'
              .rated-ago.row
                .col-sm-12
                  = review['time_ago'] || time_ago_in_words(review['updated_at']) + ' ago'
              #modal_star.stars.margin-left-top
                .ratings-score.row
                  .star.col-sm-12{dscore: review['rating'].to_i}
              .modal-review-text
                %p
                  = review['review']
              %a.close-review-more.close-reveal-modal{"aria-label" => "Close"} ×

- if check_for_alternate_tab?
  :javascript
    $('.review-container .tabbed-view .review-row .block .review-text').css('height', 'auto');
- elsif (params[:controller] == 'designs' && params[:action] == 'reviews')
  = javascript_include_tag "reviews"

- unless design_details_page?
  :javascript
    $('.star').raty({readOnly: true, score: function() {return $(this).attr('dscore');},
      starOff: "#{asset_path('star-off.jpg')}",
      starOn: "#{asset_path('star-on.jpg')}",
      starHalf: "#{asset_path('star-half.jpg')}",
      path: ''});
