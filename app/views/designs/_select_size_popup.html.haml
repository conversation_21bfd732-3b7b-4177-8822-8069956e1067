.select-size Size
- design['variants'].each do |variant|
  - variant_quantity = variant['quantity']
  -next if variant_quantity <= 0
  - class_name = parent_design_variant ? 'variant' : 'variant_modal'
  %a.button.secondary.tiny{class: class_name, value: design['id'],  :id => "#{variant['id']}",data: {old_price: "#{get_symbol_from(@hex_symbol)}#{number_with_delimiter(variant['price'])}",price: "#{get_symbol_from(@hex_symbol)}#{number_with_delimiter(variant['discount_price'])}"}}
    - if variant['option_type_values'].present?
      = variant['option_type_values'].first['name']
      - if variant_quantity < 4
        .variant-qty-left-msg= variant_quantity.to_s + ' left'
- if parent_design_variant
  = render partial: 'designs/add_to_cart_button' ,locals: {stitching_label: ''}
- else
  .select-addon-variant.btn{value: design['id']}
    %strong Select