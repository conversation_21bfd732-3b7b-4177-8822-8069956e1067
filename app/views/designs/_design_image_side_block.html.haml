.small-centered.columns
  .listing_panel_block.padding-6
    .row.collapse.pb
      .small-12.columns
        %h1#design_title.font_greyed.pdp-heading
          - if STITCHING_LABEL_DESIGNABLE_TYPE.to_a.include?(@design['designable_type']) && !@design['title'].downcase.include?(stitching_label)
            #{@design['title'].titleize} #{stitching_label.camelize}
          - else
            #{@design['title'].titleize}
        - if !@design['mirraw_recommended'] && @design['mirraw_certified']
          = image_tag('mirraw_certified.png', height:55, width: 75, class: 'mirraw_cert_logo')
      .small-1.columns.share-icon-flex
        =render partial: '/wishlists/wishlist_forms', locals: {design: @design}
        =render partial: '/designs/share_button'
    - is_prepaid_offer = (prepaid_message = PrepaidPaymentPromotion.design_message(@country_code)).present?
    - prepaid_shipping_offer = @country_code == 'IN' && DOMESTIC_PREPAID_SHIPPING_PROMOTION
    - if @design['product_offer'].present?
      - is_flash_deals = @design['product_offer']['type'] == 'flash_deals'
      - is_bmgn_product = @design['product_offer']['type'] == 'bmgnx'
      - is_qpm_active = @design['product_offer']['type'] == 'qpm'
    .product_design_price.row.product_price_pdp
      - if is_flash_deals
        .flash-icon
      - ####### changes made for manyavar pricing adjustment ############
      -if @country_code == "IN" && @design['discount_percent'] == 0
        .product_discount_div
          .price_label_div MRP
          .product_discount_price.left
            #{get_symbol_from(@hex_symbol)}#{number_with_delimiter(@design['discount_price'])}
      -else
        .product_discount_price.left
          #{get_symbol_from(@hex_symbol)}#{number_with_delimiter(@design['discount_price'])}
      -if @design['discount_percent'] == 0
        -price_in_currency = (@design['discount_price'])
        -if @country_code == 'US' || @country_code == 'AU' || @country_code == 'GB'
          .paypal_message_pdp{:data => { "pp-message": "", "pp-amount":"#{sprintf('%.2f', price_in_currency)}" ,"pp-layout":"text", "pp-buyercountry":@country_code}}
      - if @design['discount_percent'] > 0 && @design['designer']['name'] != "Manyavar"
        .discount_details
          - if @country_code == "IN"
            .product_price.left{style: 'padding: 0 2% 0 0 ; color: rgba(48, 48, 48, 0.76); font-size: 1.1em'} MRP
          .product_price_wo_discount.left
            #{get_symbol_from(@hex_symbol)}#{number_with_delimiter(@design['price'])}
        -price_in_currency = (@design['discount_price'])
        -if @country_code == 'US' || @country_code == 'AU' || @country_code == 'GB'
          .paypal_message_pdp{:data => { "pp-message": "", "pp-amount":"#{sprintf('%.2f', price_in_currency)}" ,"pp-layout":"text", "pp-buyercountry":@country_code}}
        .percent_disc.text-center.left
          = "(#{@design['discount_percent']}% OFF)"
      - if @country_code != 'IN' && !(is_bmgn_product || is_qpm_active) && (@design['categories'].map{|d| d['id']} & Category::PRICE_MATCH_GUARANTEE_CATEGORIES).present?
        .price_match_guarantee.columns
          .price_match_image
            = link_to price_match_guarantee_tnc_url do
              = image_tag('price_match_guarantee.png', height:55, width: 75, class: 'mirraw_cert_logo')
    .changeInPriceNote
      .arrow
      %span price changed
    .variant-price-text{style: 'font-size: 16px;color: rgba(48, 48, 48, 0.76);'}
      -if @country_code == "IN"
        %h5
          %i (Inclusive of all taxes.)

  - if RATING_ENABLE
    -average_rating = @country_code == 'IN' ? @design['average_rating_domestic'].to_f : @design['average_rating_international'].to_f
    -total_reviews = @design['all_reviews'].count
    -color_condition = {one: average_rating < 2.0, two: (average_rating >= 2.0 && average_rating < 3.5), three:  average_rating >= 3.5}
    -if (average_rating > 1.0) && (@design['average_rating'].to_f > 0 || @design['total_review'].to_i > 0)
      .reviews_ratings
        .small_rating
          %span{class: 'scroll_to_reviews', role: 'button', tabindex: '0'}
            %span.average_ratings=average_rating.round(1).to_s
            %span{class: [('red-rating' if color_condition[:one]), ('orange-rating' if color_condition[:two]), ('green-rating' if color_condition[:three])]} ★
            -if total_reviews > 0
              |
              %span.rating_message
                Based on #{total_reviews} Ratings

            - #so as to make reviews visible as returns in this case will go under menu bar
      -# - if false
      -#   .review-opinion
      -#     = @design['review_opinion']
.small-centered.columns.addons
  - #base_days = DOMESTIC_SHIPPING_TIME
  - #base_days = DOMESTIC_SHIPPING_TIME #+ @designer.vacation_days_count
  - #if STITCHING_ENABLE == 'true' && @design['addon_types'].present?
  - #base_days += 5
  - #if @country_code != 'IN'
  - #base_days = INTERNATIONAL_SHIPPING_TIME #+ @designer.vacation_days_count
  - #if @design['addon_types'].present? && @design['addon_types'][0]['name'] == "Rakhi Gifts"
  - #base_days = DOMESTIC_SHIPPING_TIME

  -if @sibling_designs.present?
    = render :partial => '/designs/design_groups'
  - if @design['variants'].present? && (@design['addon_types'].blank? || [nil,'stitching'].exclude?(@design['addon_types'][0]['type_of_addon'])) #@design['addon_types'].blank?
    #variants_block.size-select
      %div.select-size
        = uk_size_for_footwear(@category.parent_id,@category.id)
        - if @design['variant_size_chart'].present?
          %span.size-chart-text
            %span
              %button#dynamic-size-chart-btn.btn-view-size{"data-reveal-id" => "dynamicSizeChartModal",onClick: 'openSizeChart()'} Size Chart
              #dynamicSizeChartModal.reveal-modal{data: {reveal:"", v_offset: "0"}, style: 'padding: 0px;overflow: scroll; position:fixed;'}
                = render partial: '/designs/design_size_chart', locals: { size_chart: @design['variant_size_chart'].merge('is_variant' => true) }
    -if @design['categories'].present? && @design['categories'][0]['id'] != 127
      - change_delivery_date = @design['variants'].collect{|v| v['id'] if v['in_warehouse']}.uniq.compact
      - @design['variants'].each do |design|
        - variant_quantity = design['quantity']
        - if variant_quantity <= 0
          - #%a.variant.button.secondary.round.tiny.line_through_text.disabled{:id => "#{design['id']}"}
          - #design['option_type_values'].first['name']
        - else
          -if change_delivery_date.present?
            - if change_delivery_date.include?(design['id'])
              - delivery_date = Time.now.advance(days: design['variant_estimated_delivery_days']).strftime('%d %b %Y')
              - ready_to_ship = 'true'
            - else
              - delivery_date = (Time.now + @design['estimated_delivery_days'].day).strftime('%d %b %Y').to_s
              - ready_to_ship = 'false'
            %a.variant.button.secondary.tiny{:id => "#{design['id']}",data: {old_price: "#{get_symbol_from(@hex_symbol)}#{number_with_delimiter(design['price'])}",price: "#{get_symbol_from(@hex_symbol)}#{number_with_delimiter(design['discount_price'])}", delivery_date: delivery_date, ready_to_ship: ready_to_ship }}
              - if design['option_type_values'].present?
                = design['option_type_values'].first['name']
                - if variant_quantity < 4
                  .variant-qty-left-msg= variant_quantity.to_s + 'left'
          -else
            %a.variant.button.secondary.tiny{:id => "#{design['id']}",data: {old_price: "#{get_symbol_from(@hex_symbol)}#{number_with_delimiter(design['price'])}",price: "#{get_symbol_from(@hex_symbol)}#{number_with_delimiter(design['discount_price'])}"}}
              - if design['option_type_values'].present?
                = design['option_type_values'].first['name']
                - if variant_quantity < 4
                  .variant-qty-left-msg= variant_quantity.to_s + ' left'
    -else
      %select#variantselect
        %option --select--
        - change_delivery_date = @design['variants'].collect{|v| v['id'] if v['in_warehouse']}.uniq.compact
        - @design['variants'].each do |design|
          - variant_quantity = design['quantity']
          - if variant_quantity <= 0
            - #%a.variant.button.secondary.round.tiny.line_through_text.disabled{:id => "#{design['id']}"}
            - #design['option_type_values'].first['name']
          - else
            -if change_delivery_date.present?
              - if change_delivery_date.include?(design['id'])
                - delivery_date = Time.now.advance(days: design['variant_estimated_delivery_days']).strftime('%d %b %Y')
                - ready_to_ship = 'true'
              - else
                - delivery_date = (Time.now + @design['estimated_delivery_days'].day).strftime('%d %b %Y').to_s
                - ready_to_ship = 'false'
              -# %a.variant.button.secondary.tiny{:id => "#{design['id']}",data: {old_price: get_price_with_symbol(design['price'], @design['symbol']),price: get_price_with_symbol(design['discount_price'], @design['symbol']), delivery_date: delivery_date, ready_to_ship: ready_to_ship }}
                - if design['option_type_values'].present?
                  = design['option_type_values'].first['name']
                  - if variant_quantity < 4
                    .variant-qty-left-msg= variant_quantity.to_s + 'left'
            -else
              %option.variant.carpet_variant.button.secondary.tiny{:id => "#{design['id']}",data: {old_price: "#{get_symbol_from(@hex_symbol)}#{number_with_delimiter(design['price'])}",price: "#{get_symbol_from(@hex_symbol)}#{number_with_delimiter(design['discount_price'])}" , category_id: @design['categories'][0]['id']}}
                - if design['option_type_values'].present?
                  = design['option_type_values'].first['name']
                  - if variant_quantity < 4
                    .variant-qty-left-msg= variant_quantity.to_s + ' left'
  = render partial: 'designs/variant_selection_modal', locals: {design: @design, parent_design_variant: true}
  - if STITCHING_ENABLE == 'true' && @design['addon_types'].present?
    - if false #Promotion.stitching_offer_on?
      .stitching_offer= "Offer: Stitching cost #{get_price_with_symbol(get_price_in_currency(Promotion.stitching_offer[0],@rate), @symbol)} for products above #{get_price_with_symbol(get_price_in_currency(Promotion.stitching_offer[1],@rate),@symbol)}"
    #design_addons.design-add-on
      - if [nil,'stitching'].include?(@design['addon_types'][0]['type_of_addon'])
        .hsw-block
          Select Stitching Options
          =link_to 'i', '/pages/stitching_information'
          - if @design['stitching_combo_message'].present?
            .hsw-block{style: 'font-size: 12px'}
              %b=@design['stitching_combo_message']
      .row
        %form#design_addon_form
          .row
            .columns
              - unstitch_variant_present = @design['unstitched_variant_id'].present?
              - addon_types = @design['addon_types']
              - currency = get_symbol_from(@hex_symbol)
              - addon_types.map do |key|
                .columns
                  = "Select #{key['name']}"
                  - font_size = browser.safari? ? "font-size:14px" : "inherit"
                  %select.addon_types{:style => font_size, :data => {"name" => key['name']}, "aria-label" =>  key['name']}
                    %optgroup{:label=>'Select Addon'}
                      -if ['color','length'].include?(key['type_of_addon'])
                        %option{:popup => 'false', value: ''} select
                      - key['addon_type_values'].map do |type_value|
                        - option_value = type_value['p_name']
                        -next if Design::STITCHING_WITH_VARIANT_DESIGNABLE_TYPES.include?(@design['designable_type']) && ((!unstitch_variant_present && type_value['description'] == 'addons_with_variants') || (unstitch_variant_present && type_value['description'] != 'addons_with_variants'))
                        - delivery_time = (Time.now + type_value['delivery_days'].to_i.day).strftime('%d %b %Y').to_s
                        - rts_date = (Time.now + type_value['rts_prod_time'].to_i.day).strftime('%d %b %Y').to_s
                        - if type_value['price'].present? && type_value['price'].to_f != 0
                          - option_value += " - Additional #{currency}#{type_value['price']}"
                        - elsif ['color','length'].exclude?(key['type_of_addon'])
                          - option_value += " - Free"
                        %option{:popup =>( 'false' if ['Unstitched Blouse','Dress Material Only','Semi Stitched Material','Unstitched Material'].include?(type_value['p_name'])), :value => type_value['id'], :selected => (true if ["Standard Stitching","Regular Blouse Stitching"].include?(type_value['p_name']) && Design::UNSTITCH_POPUP_ENABLE),:class => [("standard" if ["Standard Stitching","Regular Blouse Stitching"].include?(type_value['p_name']) ), ('standard-stitch-variant' if type_value['price'].to_f == 0 && unstitch_variant_present && type_value['p_name'].downcase.include?('standard')) ,('custom' if type_value['p_name'].include?('Custom')),('petticoat_stitching' if type_value['p_name'].include?('No Petticoat')),check_addon_type(type_value['p_name'].downcase),('pre_stitch' if type_value['p_name'].include?('Non Pre-Stitched'))], :id =>('unstitch' if ['Unstitched Blouse','Dress Material Only','Semi Stitched Material','Unstitched Material'].include?(type_value['p_name'])), :data =>{:prodtime => type_value['prod_time'].to_i, delivery_date: delivery_time, rts_date: rts_date, ready_to_ship: type_value['rts'].to_s, variant_id: (type_value['p_name'].include?('Custom') ? @design['unstitched_variant_id'] : nil), old_price: (unstitch_variant_present && type_value['p_name'].include?('Custom') ? get_price_with_symbol(@design['unstitched_variant_price'], @design['symbol']) : nil),price: (unstitch_variant_present && type_value['p_name'].include?('Custom') ? get_price_with_symbol(@design['unstitched_variant_discount_price'], @design['symbol']) : nil)}}
                          = option_value
                  .row{class:('hide' if key['name'].include?('Petticoat') || (!Design::UNSTITCH_POPUP_ENABLE && !unstitch_variant_present))}
                    .small-12.medium-12.columns
                      - key['addon_type_values'].map do |option_type|
                        -availabel_sizes = option_type['standard_stiching_sizes']
                        - if option_type['addon_option_types'].any? || (!unstitch_variant_present && availabel_sizes.present?)
                          -size_chart = option_type['size_chart']
                          - if availabel_sizes.present?
                            .row.addon_option_types.hide{:id => "atv_#{option_type['id']}"}
                              .size-chart-text.plus_size_regular
                                %span.size-text=(@design['designable_type'] == 'SalwarKameez') ? 'Select Bust Size': 'Select Size'
                                %span{style: "margin-left: 12px;font-size: 13px;"}
                                  %button#size-chart-btn.btn-view-size{"data-reveal-id" => "sizeChartModal",onClick: 'openSizeChart()'} Size Chart
                                  #sizeChartModal.reveal-modal{data: {reveal:"", v_offset: "0"}, style: 'padding: 0px;overflow: scroll; height: 100%; top: 0; position:fixed;'}
                                    = render partial: '/designs/design_size_chart', locals: {availabel_sizes: availabel_sizes, size_chart: size_chart}
                              .size-chart-div.col-lg-12.col-md-12.col-sm-12.col-xs-12.nopadding
                                .row
                                  .size-chart.plus_size_regular
                                    - option_type['standard_stitch_sizes'].each do |size|
                                      %a.size.standard_size.button{id: "#{size['size_name']}", data: {size_id: size['id'], delivery_date: (Time.now + size['delivery_days'].day).strftime('%d %b %Y').to_s, prodtime: size['prod_time'], ready_to_ship: size['rts']}}= size['size_name']
                                  - if option_type['p_name'] == "Regular Blouse Stitching" && ENV['PLUS_SIZE_SHOW'] == 'true' && @design['designable_type'].to_s.downcase == 'saree' && option_type['description'].to_s == 'addons_saree'
                                    %a.plus_size_blouse_regular.plus_size_regular{:href => "#", :onclick => "return false;"} Plus Size
                                    .hsw-block.plus_size_info.plus_size_regular
                                      %a.plus_size_info_i{href:"#", 'data-reveal-id' => "plus_size_regular_info"} i
                                      .plus_size_info_modal.reveal-modal{"id" => "plus_size_regular_info", "aria-hidden" => "true", "aria-labelledby" => "modalTitle", "data-reveal" => "", :role => "dialog"}
                                        %a.close-reveal-modal{"aria-label" => "Close", href: "#", role: "button"} ×
                                        %ul
                                          - PLUS_SIZE['PLUS_SIZE_INFO'].each do |ps_info|
                                            %li #{ps_info}
                                -if option_type['rts_sizes'].present?
                                  .row.hsw-block
                                    .show_rts_sizes
                                      %input.Mcustom-checkbox.filled-in#show_rts_products{type: 'checkbox', data: {rts_sizes: option_type['rts_sizes']}}
                                      %label{for: 'show_rts_products'} Show me Ready To Ship Sizes
                              -if @design['designable_type'] == 'SalwarKameez'
                                %span#salwar_kameez_default
                                  *Size that you are selecting is body measurement Bust Size. 2-3 inch loosing will be added further.
                                  %br
                                  Example: If you select Bust Size:34 , Garment that will be made will have a Bust Size of 36 inches.
                                %span#salwar_kameez_specific{style: 'display:none;'}
                                  The garment that will be made will have a bust size of
                                  %var
                                  to
                                  %var
                                  inches as 2-3 inches loosing will be provided pertaining to the body measurement you selected.
                              .row.standard_option_values.common_addons{style: "display:none"}
                                .columns
                                  - option_type['addon_option_types'].each_with_index do |option_value,index|
                                    - next if option_value['p_name'] == "Select Blouse Size" || !option_value['published']
                                    - height_addon = option_value['p_name'].downcase == 'select your height' && @design['designable_type'].try(:downcase) == 'salwarkameez'
                                    - show_regular_plus_options = (option_type['p_name'] == "Regular Blouse Stitching" && ["select plus size","select fabric color"].include?(option_value['p_name'].to_s.downcase) && ENV['PLUS_SIZE_SHOW'] == 'true')

                                    %div{class: ('regular_plus_options' if show_regular_plus_options)} 
                                      .plus_size_regular
                                        %span.atov-name
                                          = option_value['p_name']
                                        - if option_value['price'] > 0
                                          %span.plus_size_additional_price
                                            = "-Additional"+" "+"#{get_symbol_from(@design['hex_symbol'])}"+" "+option_value['price'].to_s
                                        - if option_value['option_type'].to_s == 'radio' && show_regular_plus_options && !option_value['addon_option_values'].blank?
                                          .plus_size_color_pallete{:data =>{"name": option_value['p_name']}}
                                            = render partial: '/designs/fabric_color_options', locals: { option_value: option_value, type: "render_fabric_option"}
                                        - else
                                          .dropdown-div{:data =>{"name": option_value['p_name']}}
                                            %select{:id => "atov_#{index}", class: [('aov_select' if height_addon), ('plus_size_regular_select' if show_regular_plus_options)] }
                                              %option{:value => '0'}
                                                = option_value['p_name']
                                              - option_value['addon_option_values'].map do |addon_option|
                                                %option{:value => addon_option['id'], :data => {:prodtime => ''}}
                                                  = addon_option['p_name']
                                    -if height_addon
                                      .hsw-block#standard_info_icon{align: 'right', style: 'display:none;'}
                                        =link_to 'i', 'javascript:void(0)'
                                        -categories_ids = @design['categories'].to_a.collect{|i| i['id']}
                                        -is_anarkali = (ANARKALI_CATEGORY_FOR_STANDARD_ADDON & categories_ids).present? ? :anarkali : :salwar_kameez
                                        -height_range = Design::HEIGHT_RANGE_FOR_STANDARD_ADDON[is_anarkali]
                                        #standard_height_notice{style: 'display:none;', data: height_range}
                                          -height_range.values.map do |img|
                                            =image_tag "#{img}.jpg", id:"std-#{img}", style: 'display:none;', height: 250, class: 'std_height_img'
                          -else
                            - if option_type['p_name'] == "Custom Blouse Stitching" && ENV['PLUS_SIZE_SHOW'] == 'true' && @design['designable_type'].to_s.downcase == 'saree' && option_type['description'].to_s == 'addons_saree'
                              .custom_select_size.plus_size_blouse_custom
                                %li Select Size
                                .row.plus_size_buttons
                                  .small-6.large-6.columns.plus_size_btn_info.plus_size_custom_regular_align
                                    %a.plus_size_blouse_custom.plus_size_custom_regular.selected_custom_plus_size.custom-size-box{:href => "#", :onclick => "return false;"} Regular (#{PLUS_SIZE['PLUS_SIZE_RANGE']['regular']['min']}-#{PLUS_SIZE['PLUS_SIZE_RANGE']['regular']['max']})
                                  .small-6.large-6.columns.plus_size_btn_info
                                    %a.plus_size_blouse_custom.plus_size_custom.other_custom_plus_size.custom-size-box{:href => "#", :onclick => "return false;"} Plus Size (#{PLUS_SIZE['PLUS_SIZE_RANGE']['custom']['min']}-#{PLUS_SIZE['PLUS_SIZE_RANGE']['custom']['max']})
                                    .hsw-block.plus_size_info.plus_size_blouse_custom
                                      %a.plus_size_info_i{href:"#", 'data-reveal-id' => "plus_size_custom_info"} i
                                      .plus_size_info_modal.reveal-modal{"id" => "plus_size_custom_info", "aria-hidden" => "true", "aria-labelledby" => "modalTitle", "data-reveal" => "", :role => "dialog"}
                                        %a.close-reveal-modal{"aria-label" => "Close", href: "#", role: "button"} ×
                                        %ul
                                          - PLUS_SIZE['PLUS_SIZE_INFO'].each do |ps_info|
                                            %li #{ps_info}
                            .row.addon_option_types.addon_option_type_without_size.common_addons{:id => "atv_#{option_type['id']}", style: "display:none", class: "addon_name_#{option_type['p_name'].parameterize.underscore}"}
                              .columns
                                - option_type['addon_option_types'].each_with_index do |option_value,index|
                                  - custom_plus_options = (option_type['p_name'] == "Custom Blouse Stitching" && ["select plus size","select fabric color"].include?(option_value['p_name'].to_s.downcase) && ENV['PLUS_SIZE_SHOW'] == 'true')
                                  %div{class: ('custom_hide_option_type' if custom_plus_options) , value: option_type['id'] }
                                    -if ['select', 'radio'].include?(option_value['option_type'])                                      
                                      - if option_value['option_type'].to_s == 'select' && option_type['p_name'].downcase == 'custom blouse stitching'
                                        %span.atov-name.plus_size_blouse_custom
                                          = option_value['p_name']
                                        .dropdown-div.plus_size_blouse_custom{:data =>{"name": option_value['p_name']}}
                                          =custom_select(index, option_value)
                                      - elsif option_value['option_type'].to_s == 'select'
                                        %span.atov-name
                                          = option_value['p_name']
                                        .dropdown-div{:data =>{"name": option_value['p_name']}}
                                          =custom_select(index, option_value)
                                      - elsif custom_plus_options && !option_value['addon_option_values'].blank?
                                        %span.atov-name.plus_size_blouse_custom
                                          = option_value['p_name']
                                        - if option_value['price'] > 0
                                          %span.plus_size_additional_price.plus_size_blouse_custom
                                            = "-Additional"+" "+"#{get_symbol_from(@design['hex_symbol'])}"+" "+option_value['price'].to_s
                                        .plus_size_color_pallete.plus_size_blouse_custom{:data =>{"name": option_value['p_name']}}
                                          = render partial: '/designs/fabric_color_options', locals: { option_value: option_value, type: "render_fabric_option"}
                                      - elsif option_type['p_name'].downcase == "shapewear"
                                        = option_value['p_name']
                                        %radio{:id => "atov_#{index}" ,:value => option_type['id'] ,:class => 'shapewear_parent_div' }
                                          .shapewear_color_pallete{:data =>{"name": option_value['p_name']}}
                                            = render partial: '/designs/fabric_color_options', locals: { option_value: option_value, type: "render_shapewear_fabric_option"}
                                    -else
                                      - option_value['addon_option_values'].map do |addon_option|
                                        -name = addon_option['p_name']
                                        -name += " - Additional #{get_symbol_from(@hex_symbol)}#{option_value['price']}" if option_value['price'].present? && option_value['price'].to_f != 0
                                        .plus_size_blouse_custom
                                          %input.Mcustom-checkbox.filled-in{type: 'checkbox', value: option_value['id'], name: "#{option_value['name']}: #{get_symbol_from(@hex_symbol)}#{option_value['price']}", id: "atov_#{option_value['id']}"}
                                          %label{for: "atov_#{option_value['id']}"}= name
                                          %br
                                - if option_type['p_name'].to_s.downcase == "shapewear" && @design['designable_type'] == 'Saree'
                                  .shapewear_size_chart_div
                                    = render partial: '/designs/shapewear_size_chart' , locals: {option_type: option_type} 
                        -elsif unstitch_variant_present && @design['variants'].present? && option_type['p_name'].to_s.downcase.include?('standard')
                          / .select_div_addons.col-sm-11.variant_addons
                          - if @design['variant_size_chart'].present?
                            .size-chart-text
                              %span.size-text Select Size
                              %span{style: "margin-left: 12px;font-size: 13px;"}
                                %button#dynamic-size-chart-btn.btn-view-size{"data-reveal-id" => "dynamicSizeChartModal",onClick: 'openSizeChart()'}  Size Chart
                                #dynamicSizeChartModal.reveal-modal{data: {reveal:"", v_offset: "0"}, style: 'padding: 0px;overflow: scroll; height: 100%; top: 0;position:fixed;'}
                                  = render partial: '/designs/design_size_chart', locals: { size_chart: @design['variant_size_chart'].merge('is_variant' => true) }

                          .size-chart-div.col-lg-12.col-md-12.col-sm-12.col-xs-12.nopadding
                            .size-chart
                              - @design['variants'].each do |design|
                                - if design['quantity'] > 0
                                  %a.variant_stitch.size.button.secondary.tiny{:id => "#{design['id']}",data: {old_price: get_price_with_symbol(design['price'], @design['symbol']),price: get_price_with_symbol(design['discount_price'], @design['symbol']) }}
                                    = design['option_type_values'].first['name']
                                    - if design['quantity'] < 4
                                      .variant-qty-left-msg= variant_quantity.to_s + ' left'
                  =render partial: '/designs/message_for_stitched_unstitched'
      - if @design['addon_types'][0]['name'] != "Rakhi Gifts"
        .row
          .small-12.medium-12.columns.stitching_note.hide
            = "Note : Measurement form will be emailed to you once order is placed."
            .stitching_delay_msg=""
  #product_id.hide= "Product ID : #{@design['id']}"
  - if RAKHI_PRE_ORDER[0] == 'true' && @design['state'] == 'in_stock' && @design['parent_categories'].include?('rakhi-online')
    .listing_panel_block.pre-order
      = "Schedule Delivery"
      .pre-order-date
        = check_box_tag 'pre-order-check', RAKHI_PRE_ORDER[1], false, class: 'accept_tos'
        %label{for: 'pre-order-check'}= RAKHI_PRE_ORDER[1]

  - if false#@design['designer']['name'] == 'Aapno Rajasthan' && @design['category_name'] == 'rakhi-international'
    .row
      .small-12.medium-12.columns.notice_class{style: 'font-size:14px;'}="* Product cannot be shipped to India."

  - if false#RAKHI_DELAY_MSG != 'false' && @design['state'] == 'in_stock' && @design['parent_categories'].include?('rakhi-online')
    .row
      .small-12.medium-12.columns.notice_class{style: 'margin-bottom: 15px;'}
        = RAKHI_DELAY_MSG.html_safe

-# - if @country_code == 'IN' && SHARE_AND_EARN_REWARD > 0
  .small-centered.columns.share-earn
    = render partial: '/designs/share_and_earn_block'
- if is_flash_deals || is_bmgn_product || !is_domestic? || is_qpm_active || is_prepaid_offer || prepaid_shipping_offer
  .avaiable-offers
    .listing_panel_block.accordion.padding-6
      .design-info-blocks
        %a.main{href: '#link_to_offers'}
          %p.available-offers-on-pdp
            Available Offers :
            %span.collapse.spec-collapse
              [-]
            %span.expand
              [+]
      #link_to_offers
        .pdp_dealtimer
          = render 'layouts/campaign_timer'
        - if is_prepaid_offer
          .offer-message
            .small-12.columns.design_offer_label
              .small-11.design_offer_message=prepaid_message
              &nbsp;
              .small-1
                %a.qpm_tnc.right{href:"#", 'data-reveal-id' => "pdTncModal"} T&C
            %br
        - if prepaid_shipping_offer
          .offer-message
            .small-12.columns.design_offer_label
              .small-11.loyalty_cashback Free Shipping on Online Payment
              &nbsp;
              .small-1
                %a.loyalty_tnc.right{href:"#", 'data-reveal-id' => "shippingTncModal"} T&C
            %br
        - if is_flash_deals
          .offer-message
            .small-12.columns.design_offer_label
              .fd_message
                %a{href: '/flash-deals', title: "View More - Flash Deals Products", data: {toggle:'tooltip', placement: 'right'}}="Flash Deals"
                &nbsp;
              %a.fd_tnc.right{href:"#", 'data-reveal-id' => "fdTncModal"} T&C
        - elsif @design['ready_to_ship'] 
          .offer-message
            .small-12.columns.design_offer_label
              %span.design_offer_message Ready To Ship
              &nbsp;
              %a.qpm_tnc.right{href:"#", 'data-reveal-id' => "rtsModal"} T&C
            %br
        - elsif is_bmgn_product
          .offer-message
            .small-12.columns.design_offer_label
              - bmgnx_msg = @design['product_offer']['msg']
              %a.small-11.b1g1{href:"/buy-m-get-n-free", title: "View More - #{bmgnx_msg} Products", data: {toggle:'tooltip', placement: 'right'}}=bmgnx_msg
              &nbsp;
              %a.b1g1_tnc.right{href:"#", 'data-reveal-id' => "bmgnTncModal"} T&C
            %br
        - elsif is_qpm_active
          .offer-message
            .small-12.columns.design_offer_label
              - qpm_message = @design['product_offer']['msg']
              .small-11.design_offer_message=qpm_message
              &nbsp;
              .small-1
                %a.qpm_tnc.right{href:"#", 'data-reveal-id' => "qpmTncModal"} T&C
            %br
        - if @design['free_shipping_eligibility'] && !is_domestic? && !(@design['categories'].map { |category| category['id'] } & EXCLUDE_FREE_SHIPPING_CATEGORIES).present?
          .offer-message
            .small-12.columns.loyalty_label
              .small-11.loyalty_cashback Free Shipping
        - if Wallet.cashback_percent > 0 && !is_domestic?
          .offer-message
            .small-12.columns.loyalty_label
              .small-11.loyalty_cashback #{Wallet.cashback_percent}% cashback
              &nbsp;
              .small-1
                %a.loyalty_tnc.right{href:"#", 'data-reveal-id' => "loyaltyTncModal"} T&C
            %br
.small-centered.columns.sor-avaiable
  .listing_panel_block.padding-6.border-left-yellow.pincode-box
    - if @design['sor_available'] || @design['variant_sor_available']
      -hidden_class = ((@design['addon_types'].blank? || @design['unstitched_in_warehouse'] || @design['variant_sor_available']) && (@design['variants'].blank?)) ? '' : 'display: none;'
      #ready_to_ship{style: hidden_class}
    - if @country_code == 'IN'
      .col-lg-12.col-md-12.col-sm-12.col-xs-12.nopadding.pincode
        = hidden_field_tag 'pdd_product_id', @design['id']
        %p Check Delivery
        .effect-1
          %span.fa.fa-map-marker.fa-sm
          %input{placeholder: "Enter Pincode", type: "text", id: 'pin_code', required: true, maxlength: 6, pattern: "[0-9]{6}"}
          %a
            %span.get-eta-form-link{id: 'check_for_pdd',type: 'button'}Check
    .delivery_block
      Estimated Delivery:
      - if @country_code != 'IN' && (@design['sor_available'] || @design['variant_sor_available'])#rts_available_country?(@actual_country)
        &nbsp;
        %del.strike_old_date{style: hidden_class}= Time.now.advance(days: @design['non_rts_time']).in_time_zone.strftime('%d %b %Y')
        -if (@design['addon_types'].blank? || @design['unstitched_in_warehouse'] || @design['variant_sor_available'])
          -delivery_date = (Time.now + @design['estimated_delivery_days'].day).strftime('%d %b %Y').to_s
        -else
          -delivery_date = Time.now.advance(days: @design['non_rts_time']).in_time_zone.strftime('%d %b %Y')
      - else
        -delivery_date = (Time.now + @design['estimated_delivery_days'].day).strftime('%d %b %Y').to_s
      %span.delivery_day.font_greyed{data: {date: delivery_date}} &nbsp; #{delivery_date}
    .pdd_error_message

      -# cash on delivery availble block start
    - if (@country_code == 'IN' && COD_MESSAGE_PDP_SHOW == 'true') || @design['cod_avail']
      .listing_panel_block.padding-6.space-top.cod
        #cod_available <b>Cash on delivery </b> is available
      -# cash on delivery availble block end
