.row
  = render partial: 'designs/shipping_info', locals: {design: @design}
.row#designable_details
  .columns.panel_content.accordion
    -if @design['description'].present?
      #design_description.listing_panel_block.design-info-blocks
        %a.main{href: '#one'}
          %p
            Product Description
            %span.collapse
              [+]
            %span.expand
              [-]
        = render partial: 'description', locals: {description: @design['description']}         
    #specifications.listing_panel_block.design-info-blocks
      %a.main{href: '#two'}
        %p
          Specifications
          %span.collapse.spec-collapse
            [-]
          %span.expand
            [+]
      #two.specifications-table
        - design_highlight_hash = get_key_spec_data(design_spec_hash)
        - if design_spec_hash.except('other').values.uniq[0].present?
          - if design_highlight_hash.any?
            %table.sub-specs-line
              - design_highlight_hash.each do |highlight, values|
                - if values.present?
                  - if highlight == 'other'
                    - values.each do |property,value|
                      %tr
                        %td.spec.first &bull;
                        %td.spec= property.titleize
                        %td.spec :
                        %td.spec= value.titleize
                  - else
                    %tr
                      %td.spec.first &bull;
                      %td.spec= highlight.to_s.titleize
                      %td.spec :
                      %td.spec= values.map{|k,v| "#{k.titleize}- #{v.titleize}"}.join(' / ')
              - if design_spec_hash[:stitching].present?
                %tr
                  %td.spec.first &bull;
                  %td.spec Stitching
                  %td.spec -
                  %td.spec= design_spec_hash[:stitching].titleize
          - design_spec_hash.except(:stitching, :seo_keys).each do |spec, values|
            - if values.present?
              .design-info-sub-blocks
                %a.sub-grp{href: "#spec-#{spec}"}
                  %p
                    %span.button-icon &raquo;
                    = "#{spec.to_s.titleize} Details"
                %table.sub-specs-table{id: "spec-#{spec}"}
                  - values.each do |key,val|
                    - if val.present?
                      %tr
                        %td.spec.first &bull;
                        %td.spec= key.titleize
                        %td.spec :
                        %td.spec= val.to_s.titleize
        - else
          %table.sub-specs-line
            - design_spec_hash['other'].each do |key, val|
              - if val.present?
                %tr
                  %td.spec.first &bull;
                  %td.spec= key.titleize
                  %td.spec :
                  %td.spec= val.to_s.titleize
    - if STITCHING_ENABLE == 'true' && @design['addon_types'].present?
      #stitching.listing_panel_block.design-info-blocks
        %a.main{href: '#three'}
          %p
            Stitching
            %span.collapse
              [+]
            %span.expand
              [-]                
        = render partial: 'stitching'
    #payments.listing_panel_block.design-info-blocks
      %a.main{href: '#four'}
        %p
          Payments
          %span.collapse
            [+]
          %span.expand
            [-]             
      = render partial: 'payments'
    #returns.listing_panel_block.design-info-blocks
      %a.main{href: '#five'}
        %p
          Returns
          %span.collapse
            [+]
          %span.expand
            [-]
      -if Design.is_luxury_category?(@design['id'])
        = render partial: 'other_returns'
      -else
        = render partial: 'returns'
    - if ['SalwarKameez','Lehenga','Saree','Other','Kurti','Bag'].include?(designable_type)
      #color-wash-care.listing_panel_block.design-info-blocks
        %a.main{href: '#six'}
          %p
            Color and Wash Care
            %span.collapse
              [+]
            %span.expand
              [-]
        = render partial: 'color_wash_care'
    - if is_domestic?  
      #seller.listing_panel_block.design-info-blocks
        %a.main{href: '#detail'}
          %p
            Seller Information
            %span.collapse
              [+]
            %span.expand
              [-]
          = render partial: 'seller_info'
    - if !is_mobile_view?
      = render partial: 'share_icon'
    - if RATING_ENABLE && @design['all_reviews'].count > 0 && @design['state'] == 'in_stock' && (@design['average_rating'].to_f > 0 || @design['review_count'].to_i > 0)
      - average_rating = @country_code == 'IN' ? @design['average_rating_domestic'].to_f : @design['average_rating_international'].to_f
      = render partial: 'review_list',locals: {average_rating: average_rating, all_reviews: @design['all_reviews'], reviews: @design['latest_reviews'], review_count: @design['review_count']}
