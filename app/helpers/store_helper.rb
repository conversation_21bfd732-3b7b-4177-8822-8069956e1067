module StoreHelper

  ADDITIONAL_OPTIONS = [:property_value_ids, :category_child_ids, :designer_ids, :option_type_value_ids, :min_discount,
                        :max_discount, :min_price, :max_price, :min_carat, :max_carat, :min_rating, :pincode, :max_odr,
                        :sort, :gender]

  def get_active_filters_count(list_val,params)
    count = 0
    if params
      list_val.each do |list|
        count = count + 1 if params.split(',').include?(list.to_s)
      end
    end
    return count
  end

  def get_next_prev_url(url,val)
    page = val.to_s
    return url.include?('?') ? ( url.include?('page=') ? url.gsub(/page=\d*/, 'page='+page) : url+'&page='+page ) : url+'?page='+page
  end

  def allow_facet_for_view?(kind, facet_name, priority)
    display_list = [/neck style/i,/sleeve/i,/stitching/i,/type/i,/designer/i,/gender/i,/category/i,/fabric/i,/color/i,/occasion/i,/work/i,/look/i,/region/i,/taste/i,/size/i]
    reg_list = Regexp.union(display_list)
    facets_info = FACETED_URL_KINDS[kind]
    if facets_info
      return false if facets_info['exclude'].delete(' ').downcase.split(',').include?(facet_name.downcase)
      return priority.nil? || priority >= 0
    else
      return facet_name.match(reg_list).present?
    end
  end

  def create_url_for_facets?(type, facet_properties)
    if type == 'checkbox'
      !facet_properties.any?{|prop| prop[:type] == 'checkbox'} && facet_properties.length < 2
    else
      facet_properties.length < 2
    end
  end

  def create_faceted_url(kind, new_property, facet_properties, current_url)
    url = current_url.dup
    if kind && FACETED_URL_KINDS[kind] && new_property.present?
      property_values = facet_properties.inject([new_property]) do |property_values, value|
        if value[:name] != new_property[:name] && (value[:type] == 'checkbox' || value[:property] != new_property[:property])
          property_values.push(value)
        end
        property_values
      end
      property_values.sort! do |x, y|
        if x[:priority].abs == y[:priority].abs
          x[:name] <=> y[:name]
        else
          x[:priority].abs <=> y[:priority].abs
        end
      end
      url += '/' unless url.last == '/'
      url += "#{property_values.map!{|pv| pv[:name]}.join('_')}-#{FACETED_URL_KINDS[kind]['name']}"
    end
    url
  end

  def create_url_parameters(facet_properties, other_options)
    url = ''
    if other_options.present?
      # Add all these additional options to URL
      ADDITIONAL_OPTIONS.each do |option|
        option_value = other_options[option]
        if option_value.present?
          case option
          when :sort
            url += "&#{option.to_s}=#{StoreController::SORT.key(option_value.to_s.downcase)}"
          when :max_carat, :min_carat, :pincode
            url += "&#{option.to_s}=#{option_value.to_s}"
          when :property_value_ids, :category_ids, :designer_ids, :option_type_value_ids
            option_value = option_value.split(',').map(&:to_i)
            if option == :property_value_ids
              option_value -= facet_properties.collect { |property| property[:id] }
            end
            url += "&#{option.to_s}=#{option_value.join(',')}" unless option_value.empty?
          when :max_odr
            url += "&#{option.to_s}=#{option_value.to_s}" unless option_value == -1
          when :min_rating
            url += "&#{option.to_s}=#{option_value.to_s}" unless option_value == 0
          when :max_price, :min_price
            unless (other_options[:min_price] == 0 && other_options[:max_price] == MAX_PRICE_PER_PRODUCT)
              url += "&#{option.to_s}=#{option_value.to_s}"
            end
          when :max_discount, :min_discount
            unless (other_options[:min_discount] == 0 && other_options[:max_discount] == 100)
              url += "&#{option.to_s}=#{option_value.to_s}"
            end
          when :gender
            url += "&#{option.to_s}=#{option_value.to_s}"
          end
        end
      end
      url[0] = '?' unless url.empty?
    end
    url
  end

  def range_filter_name(range_filter)
    case range_filter['name'].downcase
    when 'discount percent'
      'Discount'
    else
      range_filter['name']
    end
  end

  def display_text(range_filter, list, symbol, formatted_list_name)
    case range_filter['name'].try(:downcase)
    when 'price'
      "#{symbol} #{list['name']}"
    when 'discount percent'
      "#{formatted_list_name}"
    else
      list['name']
    end
  end

  def catalog_amp_dynamic_content?
    # ['sarees', 'kurtas-and-kurtis'].include?(params[:kind])
    # Enable Dynamic content for all category pages
    true
  end

  def render_seo_content?
    catalog_hit? && @seo.present? &&!(params[:facets].try(:index, 'colour-') == 0 && @seo.label == params[:kind].try(:downcase)) && !params[:page].present?
  end

  def render_seo_top_content?
    render_seo_content? && TOP_CONTENT_ENABLE.include?(params[:kind]) && @seo.top_content.present?
  end

  def render_banner_slider(banners:, category_name:, slider_id: 'category_banner_slider', container_classes: 'banner-container blaze-slider banner-blaze-container')
    return unless banners.present?
    if banners.first.try(:banner_type ) == 'listing'
      wrapper_tag = :li
      p_style = :long_webp
      class1 = 'blaze-container fr_page'
      class2 = 'blaze-track catalog_product design-detail-link'
      class3 = "blaze-item catalog_product design-detail-link"
      class4 = "category-banners plp-image-box"
    else
      wrapper_tag = :div
      p_style = :main
      class1 = 'blaze-container'
      class2 = 'blaze-track'
      class3 = 'blaze-item'
      class4 = "category-banners"
    end

    content_tag(wrapper_tag, id: slider_id, class: container_classes) do
      content_tag(:div, class: class1) do
        concat(content_tag(:div, class: class2) do
          banners.each_with_index.map do |banner, index|
            ga_data = {
              banner_id: banner.id,
              banner_name: banner.name,
              banner_destination_url: banner.link,
              banner_category: category_name.titleize
            }.to_json

            content_tag(:div, class: class3, style: 'object-fit: cover;') do
              content_tag(:div, '', class: class4, data: { ga_data: ga_data }) do
                link_to(banner.link, class: 'mp-carousel-card', bannerurl: banner.link) do
                  webp_picture_tag(
                    banner.photo,
                    p_style: p_style,
                    size: '945x200',
                    alt: banner.name,
                    style: 'width:99%'
                  )
                end
              end
            end
          end.join.html_safe
        end)

        concat(content_tag(:div, '', class: 'controls') do
          content_tag(:div, '', class: 'blaze-pagination')
        end)
      end
    end
  end
end
