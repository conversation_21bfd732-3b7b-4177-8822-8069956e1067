module Api::V1::DesignsHelper

  def properties(property_values)
    hash = {}
    property_values.group_by(&:property_p_name).each do |name, values|
      hash[name] = values.map(&:name)
    end
    hash.collect{|key, value| {type: key, value: value}}
  end

  def designable_attributes(designable)
    if designable.present?
      designable.attributes.except('id', 'created_at', 'updated_at').collect{
        |key, value| {type: key, value: value}}
    else
      []
    end
  end

  def region_is_not?(country_code = @country_code, symbol = @symbol)
    country_code == 'IN' ||  ['inr', 'rs'].include?(symbol.downcase) ?
      'international' : 'domestic'
  end

  def with_or_without_custom_addon(app_source, app_version, type)
    AddonTypeValue.is_eligible_for_checkbox_addon_types(app_source, app_version, type) ? 'with_custom_addon' : 'without_custom_addon'
  end

  def get_auto_suggestion_source(product)
    if product["doctype"] == 'IN_FIELD'
      product['unbxdAutosuggestSrc'].include?('catlevel') ? 'category_name' : 'designer_name'
    end
  end
end
