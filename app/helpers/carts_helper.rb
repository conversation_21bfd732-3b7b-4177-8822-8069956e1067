module <PERSON>ts<PERSON>el<PERSON>

  def get_cart_total_information(country_code, rate)
    totals = []
    item_total = @cart.items_total_without_addons(rate)
    bmgn_discounts_inr = @cart.bmgnx_discounts.round
    coupon_discounts = get_price_in_currency(@cart.discount)
    # Cart additional discounts contain BmGn discounts
    cart_additional_discounts = get_price_in_currency(@cart.additional_discounts(country_code) - bmgn_discounts_inr)
    discounts = cart_additional_discounts + coupon_discounts + get_price_in_currency(bmgn_discounts_inr)
    wallet_discounts = @cart.wallet_details(country_code)[:referral_amount]
    totals << {amount: item_total - get_price_in_currency(bmgn_discounts_inr), title: 'Item Total'}
    if coupon_discounts > 0
      totals << {amount: coupon_discounts, title: 'Coupon Discounts'}
    end
    if cart_additional_discounts > 0
      totals << {amount: cart_additional_discounts, title: 'Cart Discounts'}
    end
    final_price = item_total - discounts
    shipping = 0
    if country_code == 'IN' && @actual_country == 'India'
      essential_shipping, essential_total, all_essential = Order.get_essential_shipping(@cart.line_items) 
      if all_essential 
        shipping += essential_shipping
      else
        shipping += @cart.shipping_cost_currency(@actual_country, rate, final_price - essential_total) + essential_shipping
      end
    else
      shipping += @cart.shipping_cost_currency(@actual_country, rate, final_price)
    end
    addons = @cart.addons_total(rate)
    totals << {amount: addons, title: 'Customisation'} if addons > 0 || (@cart.coupon.present? && @cart.coupon.is_stitching?)
    totals << {amount: shipping, title: 'Shipping'}
    gift_wrap_price = session[:gift_wrap] && country_code != 'IN' ? get_price_in_currency(GIFT_WRAP_PRICE.to_f) : 0
    if gift_wrap_price > 0
      totals << {amount: gift_wrap_price, title: 'Gift Wrap Charge'}
    end
    grandtotal = final_price + shipping + gift_wrap_price + addons
    totals << {amount: grandtotal, title: 'Sub Total', bold: true}
    tax_percent, tax_amount, grandtotal, tax_enable = @cart.get_total_with_tax(country_code, grandtotal)
    totals << {amount: tax_amount, title: "Tax"}  if tax_enable  
    if wallet_discounts > 0 && @current_user.present? && @current_user.wallet.present? && @cart.coupon_id.nil? &&
       @current_user.wallet.referral_amount > 0 && @symbol == @current_user.wallet.currency_symbol
       grandtotal = grandtotal - wallet_discounts
      totals << {amount: wallet_discounts, title: 'Wallet Discounts', id: 'wallet_discount_price'}
    end
    totals << { amount: grandtotal, title: 'Amount Payable', id: 'grand_total_price_cart', bold: true}
    [totals, grandtotal, shipping]
  end


  def add_more_items_value_for(grandtotal, shipping)
    (DOMESTIC_SHIPPING_CHARGES.keys.last.to_i - grandtotal + shipping).round(2)
  end

  # Returns the closest offer according to difference between sub total (item total - discounts)
  # and the offer minimum amount
  def get_best_offer_for_cart_text(cart, conversion_rate, country, country_code, symbol)
    text_message, best_offers = nil, []
    discount_perc, discount_rates = Promotion.additional_discount_percent(country_code)
    free_stitching_available, free_shipping_available = cart.free_stitching_on_country?(country_code), Promotion.free_shipping_on_country?(country)
    offers_present_for_country = free_shipping_available || free_stitching_available || discount_perc[0] != 0 || @prepaid_payment_promotion
    if BEST_OFFER_THRESHOLD.to_i >= 0 && offers_present_for_country
      item_total = cart.items_total_without_addons(conversion_rate)
      bmgn_discounts = (cart.bmgnx_discounts/conversion_rate).round(2)
      coupon_discounts = get_price_in_currency(@cart.discount)
      item_total -= bmgn_discounts
      sub_total = item_total - (cart.total_discounts/conversion_rate).round(2)
      free_shipping_rate =  free_shipping_available ? (Promotion.get_free_shipping(country).first/conversion_rate).round(2) : 0
      free_stitching_rate =  free_stitching_available ? (Promotion.free_stitching_at(country_code)/conversion_rate).round(2) : 0
      ship_diff, stitch_diff = (free_shipping_rate - sub_total).round(2), (free_stitching_rate - (cart.items_total_without_addons(conversion_rate) - bmgn_discounts).to_i).round(2)
      best_offers << { type: :shipping, diff: ship_diff } if ship_diff > 0
      best_offers << { type: :stitching, diff: stitch_diff } if stitch_diff > 0
      discount_rates.each_with_index do |min_discount_amount, i|
        discount_diff = ((min_discount_amount.to_i/conversion_rate) - (item_total - coupon_discounts)).round(2)
        best_offers << { type: :discount, diff: discount_diff, index: i } if discount_diff > 0
      end
      if best_offers.present?
        if ship_diff == stitch_diff && ship_diff.between?(0, (BEST_OFFER_THRESHOLD.to_i * free_shipping_rate) / 100) && stitch_diff.between?(0, (BEST_OFFER_THRESHOLD.to_i * free_stitching_rate) / 100) && (show_stitching_offer = cart.show_stitching_offer?)
          text_message = "Add items worth #{symbol} #{stitch_diff} to get FREE SHIPPING and FREE STITCHING"
        else
          best_offer = best_offers.min { |a, b| a[:diff] <=> b[:diff] }
          if best_offer.present?
            text_message = "Add items worth #{symbol} #{best_offer[:diff]} to get "
            if best_offer[:type] == :shipping && best_offer[:diff].between?(0, (BEST_OFFER_THRESHOLD.to_i * free_shipping_rate) / 100)
              text_message += "FREE SHIPPING"
            elsif best_offer[:type] == :stitching && best_offer[:diff].between?(0, (BEST_OFFER_THRESHOLD.to_i * free_stitching_rate) / 100)
              if show_stitching_offer || cart.show_stitching_offer?
                text_message += "FREE STITCHING"
              else
                text_message = nil
              end
            elsif best_offer[:type] == :discount && best_offer[:diff].between?(0, (BEST_OFFER_THRESHOLD.to_i * (discount_rates[best_offer[:index]].to_i / conversion_rate)) / 100)
              text_message += "extra #{discount_perc[best_offer[:index]]}% off"
            else
              text_message = nil
            end
          end
        end
      end
    end
    return text_message, discount_perc, discount_rates, offers_present_for_country
  end

  def coupon_message_based_on_type(coupon_type)
    case coupon_type.downcase
    when 'shipoff'
      return 'Enjoy free shipping on your order!'
    when 'stitoff'
      return 'Enjoy free stitching on your order!'
    end
  end

  def generate_addon_details(li_addon, design)
    addon_type_value = li_addon.addon_type_value
    addon_details_list = []

    if addon_type_value.price > 0
      addon_details_list << {
        addon_note: addon_type_value.name,
        addon_price: get_price_with_symbol(addon_type_value.price_currency(@rate, design), @hex_symbol)
      }
    end
      
    addon_option_types = addon_type_value.addon_option_types.uniq

    li_addon.notes.split(',').each do |note|
      addon_option_type_note = note.split(':')[0]
      addon_option_type_note_cleaned = addon_option_type_note.strip.downcase
      match_addon = if addon_option_type_note_cleaned.include?("color")
        "color"
      elsif addon_option_type_note_cleaned.include?("size")
        "size"
      elsif addon_type_value.name.downcase.include?("petticoat") && addon_option_type_note_cleaned.include?("fabric")
        "fabric"
      end

      addon_option_type = if match_addon
        match_addon = ['SalwarKameez', 'Lehenga'].include?(design.designable_type)? 'height' : match_addon
        addon_option_types.find { |option| option.p_name.downcase.include?(match_addon) }
      end

      addon_option_name = addon_option_type_note.to_s.sub(/.*?Add/, '').sub('Select ', '')
      addon_option_value = note.split(':')[1].to_s
      if addon_option_type
        addon_note = "#{addon_option_name}(#{addon_option_value.strip})"
        addon_price = get_price_with_symbol(addon_option_type.price_currency(@rate, design), @hex_symbol)
      else
        addon_note = addon_option_name
        addon_price = addon_option_value
      end

      addon_details_list.push({ 
        addon_note: addon_note, addon_price: addon_price 
      })
    end
    addon_details_list
  end
end