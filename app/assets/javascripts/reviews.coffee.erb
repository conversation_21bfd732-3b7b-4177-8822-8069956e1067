# Place all the behaviors and hooks related to the matching controller here.
# All this logic will automatically be available in application.js.
# You can use CoffeeScript in this file: http://coffeescript.org/
# Place all the behaviors and hooks related to the matching controller here.
# All this logic will automatically be available in application.js.
# You can use CoffeeScript in this file: http://coffeescript.org/
$('.review-container .tabbed-btn-row #product-btn').click ->
  $('#site_reviews').hide()
  $(this).addClass('active-btn').removeClass('passive-btn')
  $('.review-container .tabbed-btn-row #site-btn').addClass('passive-btn').removeClass('active-btn')
  $('#product_reviews').show()
  $('.best-products').hide()
  $('.best_designs_btn').show()
  return

$('.review-container .tabbed-btn-row #site-btn').click ->
  $('#product_reviews').hide()
  $(this).addClass('active-btn').removeClass('passive-btn')
  $('.review-container .tabbed-btn-row #product-btn').addClass('passive-btn').removeClass('active-btn')
  $('#site_reviews').show()
  $('.best-products').hide()
  $('.best_designs_btn').hide()
  $('.toggle-btn').text('Top Reviews')   
  return
$(window).load ->
  $('.review-container .tabbed-btn-row #product-btn').addClass 'active-btn'
  return

$('.best_designs_btn').click ->
  if $('.all-products').is(':visible')
    $('.all-products').hide()
    $('#site_reviews').hide()
    $('.best-products').show()
    $('.toggle-btn').text('All Reviews')
  else
    $('.best-products').hide()
    $('#site_reviews').hide()
    $('.all-products').show()
    $('.toggle-btn').text('Top Reviews')

$('#write_review #submit-review-btn').click ->
  star = $('#form-star input[name=score]')[0].value
  review = $('#review-text').val()
  user_name = $(this).data('username')
  email = $(this).data('email')
  review_id = $(this).data('review-id')
  if review == '' || review.length < 2 
    alert 'Please give some review'
  else
    $('body').css('overflow-y','scroll !important')
    $('body').css('position','inherit !important')
    $.ajax createSiteReview(star, review, user_name, email, review_id)
  return

createSiteReview = (star, review, user_name, email, review_id) ->
  {
    type: 'POST'
    data:
      star: star
      review: review
    url: '/customers/reviews/create_review'
    datatype: 'json'
    success: (response) ->
      if response.success
        $('a.close-reveal-modal').trigger('click');
        if review.length > 60
          window.location = window.location.href.split('?')[0] + '?site_page=1'
        else
          if review != 'none'
            delete_element(review_id)
          create_element(star, review, user_name, email,review_id)
      else
        alert 'Something went wrong!!!'
      return
    error: (error) ->
      alert 'Something went wrong!!!'
      return
  }
create_element = (star, review, user_name, email, review_id)->
  tstar = star
  if $('#'+review_id).length == 0
    itm = document.getElementById('hidden_site_review')
    cln = itm.cloneNode(true)
  else
    cln = document.getElementById(review_id)
  cln.childNodes[1].childNodes[1].innerHTML = user_name
  cln.childNodes[3].childNodes[1].childNodes[1].setAttribute 'dscore', star
  i=0;
  while tstar > 0
    cln.childNodes[3].childNodes[1].childNodes[1].childNodes[i].setAttribute 'src', '<%= asset_path('star-on.jpg') %>'
    i+=2
    tstar-=1
  cln.childNodes[5].innerHTML = review
  cln.childNodes[3].childNodes[3].childNodes[1].innerHTML = email
  cln.id = review_id
  $('#upper_review_block').append cln
  return

delete_element = (review_id)->
  if $('#'+review_id).length > 0
    $('#'+review_id).remove()

$('.toggle_scroll').click ->
  toggle_scroll()

$('#add_review').click ->
  toggle_scroll()
  
$('.close-reveal-modal').click ->
  toggle_scroll()
  
$('.reveal-modal-bg').click -> 
  toggle_scroll()

toggle_scroll = ->
  if $('body').css('overflow-y') == 'hidden'
    $('body').css('overflow-y','scroll')
    $('body').css('position','inherit')
  else
    $('body').css('overflow-y','hidden')
    $('body').css('position','fixed')

$(document).foundation()
$(document).foundation('reveal', {animation: false});
$(document).on 'closed.fndtn.reveal', '.black-content', ->
  toggle_scroll()
  return