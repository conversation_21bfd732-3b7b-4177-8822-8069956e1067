# Place all the behaviors and hooks related to the matching controller here.
# All this logic will automatically be available in application.js.
# You can use CoffeeScript in this file: http://coffeescript.org/

# $ ->
  # ga('send', 'event', {
    # eventCategory: 'Checkout',
    # eventAction: 'order-new',
    # nonInteraction: true
  # })

$ ->
  $('.payment_options input:radio:first').prop('checked', 'checked')
  togglePrepaidPromotion()

  $('.domestic_offers_toggle').click () ->
    if $('.domestic_offers_toggle .show_toggle_text').hasClass('hidden_offer_class')
      $('.domestic_offers_toggle .show_toggle_text').removeClass 'hidden_offer_class'
      $('.offers_tab .hidden_offers').show()
      $('.domestic_offers_toggle .show_toggle_text').text 'Show Less'
      $('.domestic_offers_toggle #down_arrow_toggle').hide()
      $('.domestic_offers_toggle .hide_up').show()
      $('.domestic_offers_toggle #up_arrow_toggle').show()
    else
      $('.domestic_offers_toggle .show_toggle_text').addClass 'hidden_offer_class'
      $('.offers_tab .hidden_offers').hide()
      $('.domestic_offers_toggle .show_toggle_text').text $('#show_more_text').val()
      $('.domestic_offers_toggle #up_arrow_toggle').hide()
      $('.domestic_offers_toggle #down_arrow_toggle').show()

  toggleCodCharges = ->
    if $('input[name="order[pay_type]"]:checked').attr('id') == 'order_pay_type_cash_on_delivery'
      $('.grand_total').hide()
      $('.grand_total_with_cod.hide').show()
      $('.cod_charges').show()
    else
      $('.grand_total').show()
      $('.grand_total_with_cod.hide').hide()
      $('.cod_charges').hide()
      
  $('input[name="order[pay_type]"]').on 'click', (e) ->
    toggleCodCharges()
    togglePrepaidPromotion()
  toggleCodCharges()


  
  setCookie = (cname, cvalue, exdays) ->
    d = new Date
    d.setTime d.getTime() + exdays * 24 * 60 * 60 * 1000
    expires = 'expires=' + d.toUTCString()
    document.cookie = cname + '=' + cvalue + ';' + expires + ';path=/'
    return

  getCookie = (name) ->
    value = '; ' + document.cookie
    parts = value.split('; ' + name + '=')
    if parts.length == 2
      return parts.pop().split(';').shift()
    return

  if $('.payment_options').length > 0
    window.onload = ->
      selected_payment_option = getCookie('selected_payment_option')
      if $('#auto_select_retry_cod').val()
        $('#order_pay_type_cash_on_delivery').attr('checked', true)
      else if selected_payment_option == undefined
        $('.payment_options input:radio:first').prop 'checked', 'checked'
      else
        $('.payment_options #' + selected_payment_option).prop 'checked', 'checked'
        togglePrepaidPromotion()
        toggleCodCharges()
      if $('#wallet_payment_option').length > 0
        cod_is_enabled()
      return

    $('input[name="order[pay_type]"]').on 'click', (e) ->
      setCookie 'selected_payment_option', $('input[name="order[pay_type]"]:checked').attr('id'), 7
      return

    cod_is_enabled = ->
      if $('input[name="order[pay_type]"]:checked').attr('id') == 'order_pay_type_cash_on_delivery'
        $('.grand_total, #wallet_discount_order_page, #wallet_payment_option').hide()
        $('.cod_charges, .grand_total_with_cod.hide, #wallet_cant_be_used_for_cod').show()
        if $('#wallet_payment_option').length > 0
          amount = $('#wallet_payment_option').data()
          $('#you_pay_grand_total_with_cod').text parseFloat(amount.total) + parseFloat(amount.referralAmount) + parseFloat(amount.codCharges)
      else
        if $('#delivery_type_express').length > 0 && $('#delivery_type_express').is(':checked')
          $('#wallet_payment_option').hide()
          amount.total += data('extraa_shipping')
          express_set = true
        else
          if express_set
            amount.total -= data('extraa_shipping')
            express_set = false
          $('#wallet_payment_option').show()
        $('#wallet_discount_order_page, .grand_total').show()
        $('#wallet_cant_be_used_for_cod, .cod_charges, .grand_total_with_cod.hide').hide()
        if $('#wallet_payment_option').length > 0 && $('#wallet_return').prop('checked') != true
          amount = $('#wallet_payment_option').data()
          $('#grand_total_without_cod').text('Grand Total : '+ gon.hex_symbol + ' ' + amount.total)
          $('#grand_total_without_cod').attr('value', amount.total)
          $('#you_pay_grand_total').text gon.hex_symbol + parseFloat(amount.total)
      return

    $('input[name="order[pay_type]"]').on 'click', (e) ->
      pay_type_value = $(this).val()
      cod_is_enabled()
      return

    wallet_error = (element) ->
      element.removeProp 'checked'
      $('#wallet_error').text('*Wallet not applicable').show()
      return

    $('#wallet_return').click ->
      setWalletDiscount()
      disableExpressForWallet()
      return

  unless $('#otp-form').length
    $('#codModal').css('height', 'auto').css('min-height', 0)

  $('#new_order').submit (e, submit) ->
    if ($('#order_pay_type_cash_on_delivery').is(":checked") || $('#cashondelivery').is(":checked")) && $('#codModal').length && !submit
      e.preventDefault()
      handleCodOrder()

  $('#successButton').click () ->
    $('#new_order').trigger('submit', true)

  $(document).on 'closed.fndtn.reveal', '[data-reveal]', ->
    $("#action_buttons input[type='submit']").removeAttr 'disabled'
    $("#action_buttons input[type='submit']").attr('value', 'PLACE ORDER')
    $('#cod-otp').val('')
    $('#otp-error-msg').hide()
    $('html').removeClass('modal-open')

  $(document).on 'open.fndtn.reveal', '[data-reveal]', ->
    $('html').addClass('modal-open')
    $('#otp-phone-change-form-and-content', '#phone-change-error-msg').hide()
    $('#otp-form-and-content').show()

  $(document).on 'opened.fndtn.reveal', '[data-reveal]', ->
    $('#cod-otp').focus()

  $('#otp-form').on 'submit', (e)->
    e.preventDefault();
    $.ajax
      type: "POST"
      data:
        "cod-otp": $('#cod-otp').val()
      url: $(this).attr('action')
      dataType: 'JSON'
      success: (data, status, jqxhr) ->
        if(data['verified'] == true)
          $('#new_order').trigger('submit', true)
        else
          $('#otp-error-msg').show()

  $('#cod-otp').on 'keydown', ->
    $('#otp-error-msg').hide()

  $('#resend-otp').on 'click', ->
    $(this).attr('disabled', true).css('opacity', 0.5)
    setTimeout ->
      $('#resend-otp').attr('disabled', false).css('opacity', 1)
    ,10000
    $.ajax(generateOtp($('#order_shipping_address').val(), $('#otp-phone').val(), true))

  $('#cod-otp').on 'input', ->
    if ($(this).val()).match(/^[\d]{5}$/)
      hideKeyboard($(this))

  $('#phone-change-button').on 'click', ->
    $('#otp-form-and-content').hide()
    $('#otp-phone-change-form-and-content').show()
    $('#phone_for_otp').val($('#otp-phone').val())

  $("#otp-phone-change-form").on 'submit', ->
    if $("#phone_for_otp").val().length < 8 || $("#phone_for_otp").val().length > 15
      $('#phone-change-error-msg').show()
      return false

  $('#otp-phone-change-form').on 'submit', (e)->
    e.preventDefault();
    $.ajax
      type: "POST"
      data:
        'phone': $('#phone_for_otp').val()
        'address_id': $('#address-phone-change-form').val()
      url: $(this).attr('action')
      dataType: 'JSON'
      success: (data, status, jqxhr) ->
        if data['otp_sent']
          $('#otp-phone-change-form-and-content').hide()
          $('#otp-phone-text-value').html($('#phone_for_otp').val())
          $('#otp-form-and-content').show()
        else
          $('#phone-change-error-msg').show()

  $('#otp-phone-change-form').on 'ajax:success', (evt,data) ->
    if data['otp_sent']
      $('#otp-phone-change-form-and-content').hide()
      $('#otp-phone-text-value').html($('#phone_for_otp').val())
      $('#otp-form-and-content').show()
    else
      $('#phone-change-error-msg').show()

  $('#phone_for_otp').on 'click', ->
    $('#phone-change-error-msg').hide()

  $('#otp-form-link').on 'click', ->
    $('#otp-phone-change-form-and-content').hide()
    $('#otp-form-and-content').show()

handleCodOrder = () ->
  $('#codModal').foundation('reveal', 'open')
  if $('#otp-form').length
    $.ajax(generateOtp($('#order_shipping_address').val(), $('#otp-phone').val(), false))

generateOtp = (addressId, phone, resend) ->
  type: 'POST'
  data:
    phone:phone
    address_id:addressId
    resend:resend
  url: '/carts/generate_otp'

hideKeyboard = (element) ->
  element.attr 'readonly', 'readonly'
  element.attr 'disabled', 'true'
  setTimeout (->
    element.blur()
    element.removeAttr 'readonly'
    element.removeAttr 'disabled'
  ), 100
  return

# $ ->
  # $('.add_checkout').on 'click', (e) ->
    # if typeof fbq != 'undefined'
      # fbq('track', 'AddPaymentInfo');

updateShippingDetails = () ->
  data_tag_shipping = $('input[name="delivery_type"]:checked')
  shipping_cost = data_tag_shipping.data('shipping')
  grand_total = data_tag_shipping.data('total')
  if shipping_cost != undefined && grand_total != undefined
    $('.grand_total').html('Grand Total : ' + grand_total)
    $('#shipping_charge').html('Shipping : ' + shipping_cost)
    $('.grand_total.top').html(grand_total)
    $('#estd_days').html('Estimated Delivery : '+data_tag_shipping.data('delivery-days')+' days')
  if $('#delivery_type_express').is(':checked')
    $("#wallet_return").prop('disabled', true)
    $('#wallet_payment_option').hide();
    $('#wallet_cant_be_used_for_express_delivery').show();
  else
    $("#wallet_return").prop('disabled', false)
    $('#wallet_payment_option').show();
    $('#wallet_cant_be_used_for_express_delivery').hide();

togglePrepaidPromotion = ->
  selected_option = $('input[name="order[pay_type]"]:checked')
  if selected_option.data('domestic')
    if $('input[name="order[pay_type]"]:checked').attr('id') != 'order_pay_type_cash_on_delivery'
      if selected_option.data('prepaidShippingPromo') == 'available'
        grand_total_without_shipping = selected_option.data('grandtotalWithoutShipping')
        $('#shipping_charge').html('Shipping : ' + ('FREE').fontcolor('green').bold())
        $('.grand_total').html('Grand Total : ' + grand_total_without_shipping)
        $('.grand_total.top').html(grand_total_without_shipping)
        $('#prepaid_discount').val(gon.prepaid_discount)
        $('.prepaid_discount').show()
      else 
        $('#shipping_charge').html('Shipping : ' + selected_option.data('shipping'))
        if selected_option.data('prepaidPromotion') 
          grand_total_with_prepaid_discount = selected_option.data('grandtotalWithPrepaidDiscount')
          $('.grand_total').html('Grand Total : ' + grand_total_with_prepaid_discount)
          $('.grand_total.top').html(grand_total_with_prepaid_discount)
          $('#prepaid_discount').val(gon.prepaid_discount)
          $('.prepaid_discount').show()
        else
          grand_total = selected_option.data('grandtotal')
          $('.grand_total').html('Grand Total : ' + grand_total)
          $('.grand_total.top').html(grand_total)
          $('#prepaid_discount').val(0)
          $('.prepaid_discount').hide()
    else
      $('#shipping_charge').html('Shipping : ' + selected_option.data('shipping'))
      $('#prepaid_discount').val(0)
      $('.prepaid_discount').hide()


$ ->
  updateShippingDetails()
  disableExpressForWallet()

$ ->
  $('input[name="delivery_type"]').on 'click', (e) ->
    updateShippingDetails()
  if window.innerWidth <= 1024
    $(window).scroll ->
      stickyButton(action_buttons,totals_block,2.1)
    
disableExpressForWallet  = () ->
  if $('#wallet_return').length > 0 && document.getElementById("wallet_return").checked
    $('#express_delivery_division').hide();
    $('#express_delievery_cannot_be_applied_for_wallet').show();
  else if $('#delivery_type_express').length==1
    document.getElementById('express_delivery_division').style.display = 'block'
    $('#express_delievery_cannot_be_applied_for_wallet').hide();


