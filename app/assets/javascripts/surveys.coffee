# Place all the behaviors and hooks related to the matching controller here.
# All this logic will automatically be available in application.js.
# You can use CoffeeScript in this file: http://jashkenas.github.com/coffee-script/
$ ->
  $('.rating-tab #next_nps_bifurcation').on 'click', (e) ->
    e.preventDefault();
    star_value = $('#survey_star_value').val();
    order_number = $('#survey_order_number').val()
    $.ajax
      type: 'POST'
      data:
        star_value: star_value
        survey:
          order_number: order_number
          email: $('#survey_email').val()
          token: $('#survey_token').val()
        sample_form: location.href.includes('sample_form')
      url: '/surveys'
      datatype: 'JSON'
      success: (data) ->
        if data.star_value == false
          $('#survey_alert').text(data.notice)
          $('#survey_alert').slideDown 500, ->
            setTimeout (->
              $('#survey_alert').slideUp 500
            ), 3000
        else 
          $('.rating_div').fadeOut 500, ->
            setTimeout (->
              GetQuestions(star_value, order_number)
              $('.text-message').fadeIn 500
            ), 100
      error: (data) ->
        $('#survey_alert').text(data.Error)
        $('#survey_alert').slideDown 500, ->
          setTimeout (->
            $('#survey_alert').slideUp 500
          ), 3000

  GetQuestions = (star, order_number) ->
    if star < 7
      audience = 'detractors'
      audience_text = 'We would like to understand the reason for your rating.'
    else
      if star < 9
        audience = 'neutral'
        audience_text = 'We would like to understand the reason for your rating.'
      else
        $('#last-box').data('type','promoters')
        audience = 'promoters'
        audience_text = 'Help us improve our services, your reviews are incredibly helpful to us!'
    $.ajax
      type: 'GET'
      data:
        audience: audience
        order_number: order_number
        sample_form: location.href.includes('sample_form')
      url: '/surveys/get_audience_questions'
      datatype: 'script'
      success: (data) ->
        if data.status == 422
          $('.alert-danger').text(data.notice)
          $('.alert-danger').slideDown 500, ->
            setTimeout (->
              $('.alert-danger').slideUp 500
            ), 3000
        else
          $('#audience-text').text(audience_text);
      error: (data) ->
        $('.alert-danger').text('Something went wrong.')
        $('.alert-danger').slideDown 500, ->
          setTimeout (->
            $('.alert-danger').slideUp 500
          ), 3000

  $ ->
    $('.text-message .response-button').on 'click', (e) ->
      e.preventDefault();
      if $.trim($("#survey_notes").val()) == ''
        $('#survey_alert').text("Please fill in comments")
        $('#survey_alert').slideDown 500, ->
          setTimeout (->
            $('#survey_alert').slideUp 500
          ), 3000
      else
        $.ajax
          type: 'POST'
          data:
            survey:
              notes: $('#survey_notes').val()
              order_number: $('#survey_order_number').val()
          url: '/surveys/update_notes'
          datatype: 'JSON'
          success: (data) ->
            $('.survey').remove();
            $( '.survey-message').show().text(data.notice);
          error: (data) ->
            $('#survey_alert').text(data.Error)
            $('#survey_alert').slideDown 500, ->
              setTimeout (->
                $('#survey_alert').slideUp 500
              ), 3000
$ ->
  $('.rating-tab #next_nps_bifurcation').on 'click', ->
    order_number =  $('#survey_order_number').val()
    path = '/surveys/get_line_item_image?order_number=' + order_number + '&sample_form=' + location.href.includes('sample_form')
    $.ajax
      url: path
      type: 'GET'
      datatype: 'script'
      success: (data) ->
      error: (error) ->
    return    

$ ->
  $(".review_text input[type = 'radio']").prop 'checked', false

# $ ->
#   $('.rating-tab-design .grey-stars-design').hover (->
#     star_value_design = $(this).data('star-value-design');
#     row_number = $(this).data('row-number');
#     $('.grey-stars-' + row_number).each ->
#       if $(this).data('star-value-design') <= star_value_design && $(this).css('color') == 'rgb(53, 53, 53)'
#         $(this).css 'color', '#FFC315'
#   ), ->
#     $('.rating-tab-design .grey-stars-design').each ->
#       if $(this).css('color') == 'rgb(255, 195, 21)'
#         $(this).css 'color', '#353535'
# $ ->
#   $('.rating-tab-design .grey-stars-design').on 'click', (e) ->
#     star_value_design = $(this).data('star-value-design');
#     row_number = $(this).data('row-number');
#     $(this).closest('.questions').find('.grey-stars-design').each ->
#       $(this).removeAttr('selected')
#       if $(this).data('star-value-design') <= star_value_design
#         $(this).css 'color', '#FFD316'
#       else
#         $(this).css 'color', '#353535'
#     $(this).attr('selected', 'true')

#$ ->
  #$('.rating-tab-design .grey-stars-design').on('click', (e) ->
    #e.preventDefault();
    #star_value_design = $(this).data('star-value-design');
    #design_id = $(this).data('design-id');
    #row_number = $(this).data('row-number');
    #order_id = $(this).data('order-id');
    #$.ajax
      #type: 'POST'
      #data:
        #star_value_design: star_value_design
        #design_id: design_id
        #order_id: order_id
        #survey:
          #order_number: $('#survey_order_number').val()
      #url: '/surveys/new_review'
      #datatype: 'JSON'
      #success: (data) ->
        #$('.review_text_' + row_number).show();
        #$('#review_issue_'+row_number).show();
        #if star_value_design >= 4
          #$('.submit_review_and_share_done').show()
        #$('.rating-tab-design-' +row_number).hide();
      #error: (result) ->
        #$('#survey_alert').text(data.notice)
  #)

# $ ->
#   $('.submit_review_text_done').on 'click', (e) ->
#     e.preventDefault()
#     row_number_text = $(this).data('row-number-text')
#     product_id = $(this).data('product-id')
#     order_id = $(this).data('order-id')
#     comment_data = $('#customer_review_' + row_number_text).val()
#     # issue_type = []
#     # selected_issue = {}
#     # $(".survey-questions-answers input:checked").each ->
#     #   selected_issue[$(this).attr('name').split('_')[0]] = $(this).attr('value')
#     #   issue_type.push($(this).data('issue-type'))
#     $.ajax
#       type: 'POST'
#       url: '/surveys/save_review_text'
#       data:
#         comment: comment_data
#         order_id: order_id
#         product_id: product_id
#         #issue: jQuery.unique(issue_type).join(',')
#         #answers: selected_issue
#       success: (result) ->
#         $('.review_table_' + row_number_text).remove();
#         if $('.review_table').length == 0
#           $('#review_designs').text('Thank You!');
#           $('.review-the-products').remove()
#           if $('#last-box').data('type') == 'promoters'
#             $('#last-box').show()
#         return
#       error: (result) ->
#         $('#survey_alert').text(data.notice)
#         return
#     return

# $ ->
#   $('.submit_review_text_cancel').on 'click', (e) ->
#     e.preventDefault()
#     row_number_text = $(this).data('row-number-text')
#     $('.review_table_' + row_number_text).remove();
#     if $('.review_table').length == 0
#       $('#review_designs').text('Thank You!');
#       if $('#last-box').data('type') == 'promoters'
#         $('#last-box').show()
#       $('.review-the-products').remove()
$ ->
  $(document).on 'ready', ->
    $('#no_of_rating_nps').text($('#slider').attr('data-slider')+'.0')
    return
  $(document).foundation slider: on_change: ->
    if $('#slider').attr('data-slider') == '10'
      $('#no_of_rating_nps').text($('#slider').attr('data-slider'))
    else
      $('#no_of_rating_nps').text($('#slider').attr('data-slider')+'.0')
    return
$ ->
  $('.bifurcation').on 'click', (e) ->
    row_number = $(this).attr('id').split('_')[1]
    if $('#sub-'+@id+' ul li').length > 0
      $('#review_issue_'+row_number+' .issues .sub-bifurcation, #review_issue_'+row_number+' .issues .bifurcation, #close_'+row_number).hide()
      $('#close_'+row_number).show()
      $('#sub-'+@id).show()
    else
      showBifurcation(row_number)


  showBifurcation = (id) ->
    $('#review_issue_'+id+' .issues .bifurcation').show()
    $('#close_'+id).hide()

  $('.back').on 'click', (e) ->
    row_number = $(this).attr('id').split('_')[1]
    $('#review_issue_'+row_number+' .issues .sub-bifurcation, #close_'+row_number).hide()
    $('#review_issue_'+row_number+' .issues .bifurcation').show()
    $('html, body').animate({
      scrollTop: $('#review_issue_'+row_number+' .issues .bifurcation').offset().top - 250
    }, 100);
$ ->
  $('.number_star').click ->
    $('#next_nps_bifurcation').show()
    $('#survey_star_value').val parseInt($(this).text())
    $('.number_star').css
      'background-color': '#ffffff'
      'color': '#000000'
    if parseInt($(this).text()) <= 6
      color = '#E91E63'
    else if parseInt($(this).text()) > 6 and parseInt($(this).text()) < 9
      color = '#F57C00'
    else
      color = '#009688'
    $(this).css
      'background-color': color
      'color': '#ffffff'
