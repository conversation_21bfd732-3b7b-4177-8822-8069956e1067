$bluish_black: #010710;
$dark_blue: #424242;
$panel_box_shadow: none;
$border_gray: 2px solid #bfbfbf;
$text_pink: #ca3366;
$dark_green: #0e9a7d;
$green_btn_box: 0px 3px 1px -1px #670b19;
$red_btn_box: 0px 3px 1px -1px #4e201d;
$pink_btn: #d44a70;
$pink_btn_box: 0px 3px 1px -1px #71243e;
$gray: #4d4d4d;
$gray_bg: #71706f;
$green_font : white;
$facebook-color: #3b5998;
$twitter-color: #55acee;
$google-color: #d50f25;
$text-transform: capitalize;
$icon-size: 2em;
$variant_color: #2b2a2a;
$light_orange : #ffaa5d;

$border_black: 1px solid #d8d8d8;
$text_white: #ffffff;
$light_red : #b11f2d;
$menu_background: #8f1b1d;
$red_background: #8f1b1d;
$dark_red: #670b19;
$text_red: #8f1b1d;
$body_white: #ffffff;
$text_black: #303030;
$add_to_cart_red: #8f1b1d;
$cart_font: 0.8125rem;
$menu_font_size: 0.8rem;
$font_size: 0.750rem;
$small_font: 0.675rem;
$light_grey: #f4f4f4;
$big_font: 0.875rem;
$input_focus_color: #670b19;
$placeholder_color: #615f5f;
$gray_btn_box: 0px 3px 1px -1px grey;
$seo_p_font_size: 0.875rem;

$light-red-bg: #b11f2b;
$dark-red-text: #670b19;
$dark-grey: #d4d4d4;
$global-text-color: #303030;
$light-red-text: #b11f2b;
$light-red-background: #7b0e1d;
$light-grey: #eeeeee;
$light-border-box: 1px solid #eeeeee;
$light-dashed-border: 1px dashed #e7e8e8;

/**
 * An attempt to normalize color variable names and hopefully create
 * a color system out of it over time that's both easy to use and
 * communicate.
 */

$neutral-darkest-color: #303030;
$neutral-lightest-color: #f4f4f4;