//importing variables
@import 'variables';
@import 'black_theme';
body {
  font-family: "Inter", sans-serif !important;
}
.ebox-recommend, .recommended-design-box{
  border-top: 1px solid #636060;
  border-bottom: 1px solid #636060;
  margin: 0 auto;
  background-color: transparent;
  box-shadow: none;
  max-width: 65.5rem;
  padding: 8px 0px;
  .title-block{
    h1{
      color: $text_black;
      font-size: 18px;
      text-transform: capitalize;
    } 
  }
}
.ebox-recommend{
  .unbxd-img-loader{
    display: inline-block;
    width: 49%;
    margin-bottom: 10px;
    .image-container{
      text-align: center;
      background-color: $snow_white;
      padding: 15px 0px;
    }
  }
  .product-1{
    padding-right: 4px;
  }
  .product-2{
    padding-left: 4px;
  }
}

.no-design-box{
  text-align: center;
  font-style: italic;
}
.recommended-design-box{
  .row{
    margin: 0 auto;
    max-width: 62.5rem;
    width: 100%;
  }
  .recommended-products{
    margin: 0px;
    .product-list{
      display: inline-block;
      margin-right: 8px;
      width: 153px;
      .product-box {
        position: relative;
        /*border: 1px solid #171616;*/
        margin: 0;
        width: 153px;
        border-radius: 2px;
        box-shadow: $card_box_shadow;
        .image-box{
          width: 151px;
          border-radius: 2px 2px 0px 0px;
          img{
            width: 100%;
            border-radius: 2px 2px 0px 0px;
          }
          .catalog-rating {
            position: absolute;
            width: 40px !important;
            height: 40px;
            margin-top:-25px;
            .small_rating{
              font-size: 12px;
              background-color: #fff;
              color: #3C4345;
              padding: 5px;
              border-radius: 5%;
              font-weight: bold;
            }
            .green-rating{
               color: #16be48;
            }
            .red-rating{
               color: #FF5722;
            }
            .orange-rating{
              color: #FFA000;
            }
          }
        }
        .panel {
          height: 3.8em;
          position: relative;
          padding:0.6em;
          background: $snow_white;
          border: none;
          margin-bottom: 0em;
          border-radius: 0px 0px 2px 2px;
          text-align: center;
          &.design_desc {
            a {
              color: white;
            }
          }
          .price-block{
            font-size: 14px;
            color: $text_black;
          }
        }
      }
    }
  }
  .scroll-btn{
    position: relative;
    font-size: 26px;
    line-height: 32px;
    background-color: $snow_white;
    color: #eaeaea;
    text-align: center;
    bottom: 160px;
    width: 5%;
    box-shadow: -1px 0px 2px 0px #383737;
    display: none;
  }
  .scroll-btn-left{
    left: 1px;
    float: right;
  }
  .scroll-btn-right{
    right: 1px;
    float: left;
  }
}

@media only screen and (max-width: 989px){
  .recommended-design-box{
    .recommended-products{
      display: -webkit-box;
      display: -ms-flexbox;
      display: flex;
      overflow-x: scroll;
      .product-list{
        margin-bottom: 2px;
      }
    }
  }
}

@media only screen and (min-width: 980px){
  .recommended-design-box{
    .row{
      text-align: center;
    }
    .recommended-products{
      overflow-x: visible;
      width: auto;
      .product-list{
        margin-bottom: 10px;
      }
    }
  }
  .scroll-btn{
    display: none;
  } 
}