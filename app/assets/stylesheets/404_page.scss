@import 'variables_red';
body{
  font-family: "Inter", sans-serif !important;
}
.page{
  .main {
   margin-bottom: 2em;
   .status-code{
    font-size: 4em;
    color: #d7d1d1;
    letter-spacing: .1em;
    line-height: 1.5;
    }
    .oops-msg{
      font-size: 1.5em;
      color: $dark_red;
    }
    .error-msg{
      line-height: 2;
      font-weight: 100;
    }
    .searches{
      display: initial;
      font-size: $big_font;
    }
    .popular-links{
      border-right: 1px solid grey;
      padding: 0 0.5em;
      font-weight: 100;
      font-size: $big_font;
    }
  }
  .home_page_designs {
    margin: 0;
    >li {
      img{
        width:100%;
      }
      .fr_page {
        border: $border_black;
        .panel {
          position: relative;
          padding:0em 0.3em 0.3em 0.3em;

          &.design_desc {
            margin-bottom: 0em;
            border: none;
            background: transparent;
            a {
              color: $text_red;
              font-size: $font_size;
              font-weight: bold;
            }
            .row{
              margin: 0 auto;
              .columns{
                padding: 0;
              }
            }
          }
        }
      }
    }
    li {
      &.original_price {
        font-size: 0.8em;
        color: $text_black;
      }
      &.discount_price {
        font-weight: bold;
        color: $text_black;
      }
      &.percent_off {
        color: red;
        margin-top: -1.7em;
        font-size: 0.9em;
      }
    }

    .rating_div{
      position: absolute;
      width: 40px !important;
      height: 40px;
      margin-top:-25px;
      .small_rating{
        font-size: 12px;
        background-color: #fff;
        padding: 5px;
        color: #3C4345;
        border-radius: 5%;
        font-weight: bold;
      }
      .green-rating{
        color: #16be48;
      }
      .red-rating{
        color: #FF5722;
      }
      .orange-rating{
        color: #FFA000;
      }
    }

    .design-col1 {
    float:left;
    width:100%;
    font-size: $font_size !important;
    font-weight: bold;
    text-align: left;
    }

    .design-col2 {
      font-size: 12px !important;   
      padding: 9px 9px;
      line-height: 12px;
      position: relative;
      color: $text_white;
      width: 54px;
      height: 40px;
      float: right;
      top: 40px;
      margin-top: -38px;
      background-position: 81% 69%;
    }

    .add-to-cart-bst{
      padding: 12px 2px;
      width: 100%;
      color: $text_white;
      background: $add_to_cart_red;
      font-weight: 700;
      /*box-shadow: $green_btn_box;*/   
    }
  }
}