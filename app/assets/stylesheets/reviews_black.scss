// Place all the styles related to the reviews controller here.
// They will automatically be included in application.css.
// You can use Sass (SCSS) here: http://sass-lang.com/
// Place all the styles related to the review controller here.
// They will automatically be included in application.css.
// You can use Sass (SCSS) here: http://sass-lang.com/
@import 'variables';
@import 'black_theme';
body {
  font-family: "Inter", sans-serif !important;
}
.page.designs_reviews, .page.reviews_site_review{
  #container{
    margin-top: 1.8em;
  }
}
#site_review_top_content{
  float:left;
  /*border: 1px dotted grey;*/
  padding: 20px;
  background-color: $snow_white;
  margin-bottom: 20px;
  margin-top: 20px;
  box-sizing: border-box;
  box-shadow: $card_box_shadow;
  width: 100%;
  #top_content {
    &.read-more{
      height:125px;
      overflow:hidden;
    }
    font-size:0.9rem;
  }
}
#review_of_design{
  background-color: $snow_white;
  box-shadow: $card_box_shadow;
  padding: 3px 0px 0 0px !important;
}
.review-container{
  width:100%;
  height:100%;
  color:$text_black;
  padding:10px;
  .heading{
    font-size:18px;
    font-weight:bold; 
  }
  .heading_review{
    padding-bottom: 8px;
    margin-left: 7px;
    .view_all{
      font-size: 15px;
      float:right;
      margin-right: 10px;
    }
  }
  .heading-content{
    font-size:15px;
    font-weight:bold;
  }
  .tabbed-btn-row{
    width:100%;
    margin-top: 2%;
    .tabbed-btn{
      width: 50%;
      text-align: center;
      vertical-align: middle;
      padding: 5px 2px;
      font-size: 16px;
      float: left;
      cursor: pointer;
      color: $text_black;
    }
    .active-btn{
      background-color: $snow_white;
    }
    .passive-btn{
      background-color: transparent;
      border: none;
    }
  }
  .tabbed-view{
    float:left;
    width:100%;
    color:$text_black;
    background-color: $snow_white;
    padding-bottom: 5px;
    .review-row:first-child{
     border-bottom:none;
    }
    #hidden_site_review{
      display:none;
    }
    .review-row{
      padding:2%;
      width:100%;
      clear:both;
      margin-bottom: -10px;
      .add-new-review{
        float:right;
      }

      .block{
        background-color: $body_white;
        box-shadow: $card_box_shadow;
        display: inline-block;
        width: 96%;
        padding: 15px 15px 0px 15px;
        margin-left: 2%;
        .margin-left-top{
          margin-top:1%;
          margin-left:8%;
        }
        .view-more-btn{
          position: relative;
          width: 100px;
          padding: 5px 3px;
          float: right;
          height: 25px;
          font-size: 12px;
          color: $link_blue;
          text-align: right;
        }

        .user-sign{
          width: 50px;
          height: 50px;
          border-radius: 50%;
          background-color: #000000;
          text-align: center;
          float: left;
          margin: 5px 0px 0px 0px;
          .user-name{
            position: relative;
            top: 25%;
            font-size: 1rem;
            .user_image{
              width: 100%;
              height: 100%;
              border-radius: 50%;
              top: -12.5px;
              position: relative;
            }
          }
        }
        .design-img{
          float: left;
          margin-top:7px;
          overflow:hidden;
          .image_block_hidden{
            width: 50px;
            height: 50px;
          }
        }
        .stars{
          width: 55%;
          float: left;
          font-size: 12px;
          .star{
          }
          .star img{
            background:none;
          }
          .user-name-full{
            overflow:hidden;
          }
          .rated-ago{
            margin-top:0px;
          }
        }
        .review-text{
          float: left;
          width: 100%;
          overflow: hidden;
          font-size: 12px;
          text-align: justify;
          padding-right: 3%;
          margin-left: 0px;
          display: -webkit-box;
          -webkit-line-clamp: 3;
          -webkit-box-orient: vertical;  
          overflow: hidden;
        }
      }  
    }
  }
  #product_reviews{
    display:block;
  }
  #site_reviews{
    display:none;
  }
}
.reveal-modal{
  color: $text_black;
  background-color: $snow_white;
}
.black-content{
  #modalTitle{
    color: $text_black !important;
  }
  #modal_star{
    border-bottom: 1px solid white;
    padding-bottom: 15px;
  }
  @media (max-height: 600px) { 
    .modal-review-text{
      height: 100%;
      overflow-y: scroll;
      
    }
  }
  @media only screen and (min-device-width:500px) and (orientation:landscape){
    .modal-review-text{
      height: 100%;
      overflow:scroll;
    }
  }
  position:absolute;
  top:0px ;
  height: 100%;
  overflow: scroll;  
}
.btn-custom-green{
  padding: 3px 12px !important;
  color: #fff;
  background-color: #2ecc71;
  border-color: #2ecc71;
}
.btn-custom-blue{
  padding: 3px 12px !important;
  color: #fff;
  background-color: #3498db;
  border-color: #3498db;
}
.btn-custom-red{
  padding: 3px 12px !important;
  color: #fff;
  background-color: #EC407A;
  border-color: #EC407A;
}
.btn-custom-green:hover{
  background-color: #27ae60;
  border-color: #27ae60;
}
.btn-custom-blue:hover{
  background-color: #2980b9;
  border-color: #2980b9;
}
.btn-custom-red:hover{
  background-color: #E91E63;
  border-color: #E91E63;
}