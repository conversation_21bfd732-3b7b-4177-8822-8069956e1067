.review-container {
    width: 100%;
    max-width: 1330px;
    margin: auto;
    .product_row {
        display: flex;
        .design-img {
            overflow: visible;
            width: 30%;
            .product_images {
                position: sticky;
                top: 20%;
                img{
                    border-radius: 6px;
                }
            }
        }
        .rating_review_box {
            width: 100%;
            margin-left: 40px;
        .review-row {
            border: 1px solid #f1f1f1;
            border-radius: 6px;
            margin-bottom: 25px;
            padding: 20px;
            box-shadow: 0 0 10px #f2f2f2;
            .user_information {
                display: flex;
                width: 100%;
                align-items: center;
                .user-sign {
                    margin-right: 18px;
                    border-radius: 50px;
                    width: 50px;
                    height: 50px;
                   align-items:  center;
                    display: flex;
                    justify-content: center;
                    img{
                        width: 100%;
                        max-width: 50px;
                        border-radius: 50px;
                    }                
                }
                .user-name-full,.rated-ago {
                    p {
                        text-transform: capitalize;
                        font-size: 11px;
                        margin: 0;
                    }
                }
                .rated-ago {
                    p {
                        font-size: 11px;
                    }
                }
            }
        }
    }
}
button {
    background: #fff9fa;
    color:#670b19;
    padding: 10px;
    border-radius: 6px;
    margin-left: 5px;
}
.pagination {
    text-align: center;
    .first, .last{
        display: none;
    }
}
}
@media only screen and (min-width: 30em)
{
    .reveal-modal {
        top: 10.25rem;
    }
}
@media screen and (max-width:991px) {
    .review-container {
        .product_row {
            flex-wrap: wrap;
            .design-img {
                width: 100%;
                text-align: center;
                margin-bottom: 25px;
            }
            .rating_review_box {
                margin-left: 0;
            }
        }
        
    }
}