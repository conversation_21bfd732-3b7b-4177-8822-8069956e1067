# == Schema Information
#
# Table name: variants
#
#  id         :integer          not null, primary key
#  design_id  :integer
#  quantity   :integer
#  position   :integer
#  created_at :datetime         not null
#  updated_at :datetime         not null
#

class Variant < ActiveRecord::Base
  has_and_belongs_to_many :option_type_values
  has_many :design_promotion_pipe_lines, through: :design
  belongs_to :design

  scope :stock_available, -> { where {quantity > 0} }

  # def self.default_scope
  #   where show_variant: true
  # end
  def price
    return (self[:price].to_i == 0 ? design.price : self[:price].to_i)
  end

  def category_ids
    design.category_ids
  end

  PRICE_ATTR = [:discount_price, :price, :effective_price]

  include Priceable
  include DesignPrice

end
