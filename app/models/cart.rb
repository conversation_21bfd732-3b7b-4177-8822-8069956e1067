# == Schema Information
#
# Table name: carts
#
#  id                 :integer          not null, primary key
#  created_at         :datetime
#  updated_at         :datetime
#  email              :string(255)
#  notified           :string(255)
#  coupon_id          :integer
#  hash1              :string(255)
#  notified_at        :datetime
#  notification_count :integer          default(0)
#  paypal_token       :string(255)
#  user_id            :integer
#  used               :boolean          default(FALSE)
#  device_id          :string(255)
#

class Cart < ActiveRecord::Base
  has_many :line_items, inverse_of: :cart
  has_many :designs, through: :line_items
  belongs_to :coupon
  belongs_to :user
  belongs_to :wallet
  default_scope { where("user_type = 'User' or user_type is null") }
  has_one :push_engage_subscriber, -> {order "id DESC"}, inverse_of: :cart
  has_one :gokwik_data
  after_initialize :set_default_user_type
  attr_accessor :skip_line_item_wallet_update
  attr_accessor :prepaid_payment_promotion

  COD_CHARGE = COD_CHARGE.to_i

  # Attributes that require currency conversion
  # Needs to always be above Priceable
  PRICE_ATTR = [:discount, :total_discounts]

  include Priceable
  include CouponValidation
  include CartPromotions
  include Taxes
  include CurrencyHelper

  before_save :generate_hash_value!

  accepts_nested_attributes_for :line_items

  # Getting discount amount
  #
  # == Returns:
  # Integer
  #
  def discount(return_type = false)
    self.coupon.present? ? self.coupon.discount_for(self, return_type) : 0
  end

  # call with force_update = true if content in cart has changed due to modifications
  def total_discounts(force_update = false)
    @total_discounts = (!force_update && @total_discounts) || (self.discount + self.additional_discounts(Design.country_code)).round(2)
  end

  def bmgnx_discounts(bmgnx_hash = PromotionPipeLine.bmgnx_hash)
    bmgnx_hash.present? ? get_free_items_bmgnx(bmgnx_hash).sum{|k,v| v[0] * v[1] * (bmgnx_hash[:x]/100.to_f)} : 0
  end

  def prepaid_discount
    prepaid_offer = PrepaidPaymentPromotion.new(self, Design.country_code) if PrepaidPaymentPromotion.active?(Design.country_code)
    prepaid_offer ? prepaid_offer.discounted_price(self.item_total_with_discount(1,Design.country_code,false,false)) : 0
  end

  def prepaid_percent
    prepaid_offer = PrepaidPaymentPromotion.new(self, Design.country_code) if PrepaidPaymentPromotion.active?(Design.country_code)
    prepaid_offer ? prepaid_offer.percent : 0
  end

  def additional_discounts(country_code)
    discount_percent, amount = Promotion.additional_discount_percent(country_code)
    total = self.items_total_without_addons(1) - self.discount
    additional_discounts = 0
    if (bmgn_discounts = self.bmgnx_discounts) > 0
      additional_discounts += bmgn_discounts
    elsif (qpm_disc_percent = self.qpm_disc_percent) > 0
      additional_discounts += total * qpm_disc_percent.to_f / 100.0
    end
    total -= additional_discounts
    promo_additional_discounts = 0
    if self.coupon.nil?
      discount_percent.each_with_index do |discount,index|
        if (discount_percent[index].to_i > 0) && (total > amount[index].to_i)
          promo_additional_discounts = total * discount_percent[index].to_i / 100
        end
      end
    end
    additional_discounts += promo_additional_discounts
    additional_discounts.round
  end

  def get_free_items_bmgnx(bmgnx_hash = PromotionPipeLine.bmgnx_hash)
    total_quantity, bmgnx_items_hash = 0, {}
    line_items.each do |item|
      if item.buy_get_free == 1
        bmgnx_items_hash[item.id] = [item.snapshot_price_currency(1), item.quantity]
        total_quantity += item.quantity
      end
    end
    if bmgnx_items_hash.present?
      bmgnx_items_hash = bmgnx_items_hash.sort_by {|k,v| -v[0]}.to_h
      factor = ((total_quantity + bmgnx_hash[:n]) / (bmgnx_hash[:m] + bmgnx_hash[:n])).to_i
      mx, nx, count = factor * bmgnx_hash[:m], factor * bmgnx_hash[:n], 0
      # total_charged_quantity are items with charge as full amount (mx)  
      total_charged_quantity = total_quantity > (mx + nx) ? (total_quantity - nx) : mx  
      # remove first mx items and keep only last nx items which are eligible for offer
      bmgnx_items_hash.each do |k,v|
        remaining = total_charged_quantity - count
        if count < total_charged_quantity
          if v[1] <= remaining
            count += v[1]
            bmgnx_items_hash.delete(k)
          else
            count += remaining
            v[1] -= remaining
            break
          end
        end
      end
    end
    bmgnx_items_hash
  end

  def get_bmgnx_notice(bmgnx_hash: PromotionPipeLine.bmgnx_hash, with_scheme_name: false)
    if bmgnx_hash.present? && (total_quantity = line_items.inject(0){|sum, item| sum += item.quantity if item.buy_get_free == 1; sum}) > 0
      factor = ((total_quantity + bmgnx_hash[:n]) / (bmgnx_hash[:m] + bmgnx_hash[:n])).to_i
      round_quantity = (bmgnx_hash[:m] + bmgnx_hash[:n]) * factor
      discount_type = bmgnx_hash[:x] != 100 ? 'at ' + bmgnx_hash[:x].to_s + '% off' : 'free'.freeze
      message = if total_quantity > round_quantity && total_quantity <  bmgnx_hash[:m] + round_quantity
                  quantity_need_to_add = bmgnx_hash[:m] + round_quantity - total_quantity
                  "Add #{quantity_need_to_add} more #{'item'.pluralize(quantity_need_to_add)} to be eligible for the B#{bmgnx_hash[:m]}G#{bmgnx_hash[:n]} offer."
                elsif total_quantity >= round_quantity - bmgnx_hash[:n] && total_quantity < round_quantity
                  "You have added #{total_quantity} #{'item'.pluralize(bmgnx_hash[:n])} in the cart. Add #{round_quantity - total_quantity} more #{'item'.pluralize(round_quantity - total_quantity)} to avail B#{bmgnx_hash[:m]}G#{bmgnx_hash[:n]}!"
                end
      message && with_scheme_name ? ("B#{bmgnx_hash[:m]}G#{bmgnx_hash[:n]} Scheme: " + message) : message
    end
  end

  def wallet_discounts(country_code, rate, paypal = false, system = false)
    discount = wallet_referral_amount(country_code) + wallet_return_amount(country_code)
    if paypal
      (discount/rate).round(2)
    elsif system
      (discount*rate).round(2)
    else
      discount.round(2)
    end
  end

  def applicable_referral_amount(country_code)
    if user.present? && (user_wallet = user.wallet).present? && country_code == user_wallet.country_code
      conversion_rate = user_wallet.currency_convert.rate
      total = total_currency(conversion_rate, nil, nil, false, false)
      _ , _ , total, _ = self.get_total_with_tax(country_code, total)
      user_wallet.usable_referral_amount(total, 2)
    else
      0
    end
  end

  def wallet_referral_amount(country_code)
    if has_wallet_for(country_code) && wallet.referral_amount > 0
      applicable_referral_amount(country_code)
    else
      0
    end
  end

  def wallet_return_amount(country_code)
    has_wallet_for(country_code) ? wallet.return_amount.round(2) : 0
  end

  def has_wallet_for(country_code)
    wallet.present? && country_code.present? && user.present? && user.wallet.present? &&
    wallet.country_code == user.wallet.country_code && country_code == wallet.country_code
  end

  def cart_coupon_apply(device_id, app_version, app_source, api_account_id = nil, fcm_registration_token)
    api_account = ApiData.find_by_device_id(device_id)
    do_notify_user = false
    if api_account && (api_account.last_cart_coupon_shown == nil or ((Time.now - api_account.last_cart_coupon_shown)/86400).to_i > CART_COUPON_CONSTANT['Cart_coupon_cycle_days'])
      api_account.update_attributes(last_cart_coupon_shown: Time.now)
      do_notify_user = true
      api_account_id ||= api_account.try(:account_id)
    elsif api_account == nil
      ApiData.create(device_id: device_id, last_cart_coupon_shown: Time.now, account_id: nil)
      do_notify_user = true
    end
    self.send_cart_timer_notification(app_version, app_source, device_id, api_account_id, fcm_registration_token) if do_notify_user
  end

  def cart_coupon_applicable?(api_account)
    api_account.last_cart_coupon_shown != nil && (Time.now <= (api_account.last_cart_coupon_shown + CART_COUPON_CONSTANT['Cart_coupon_valid_Minutes'].minutes)) ? true : false
  end

  def self.get_cart_coupon_countdown(api_account)
    (Time.now < (api_account.last_cart_coupon_shown + CART_COUPON_CONSTANT['Cart_coupon_valid_Minutes'].minutes)) ? (api_account.last_cart_coupon_shown + CART_COUPON_CONSTANT['Cart_coupon_valid_Minutes'].minutes).to_datetime.strftime('%Q') : ''
  end

  def self.get_cart_coupon_offer_msg(coupon)
    "Use coupon code : \"#{ coupon.code.upcase }\" To get an additional \"#{coupon.percent_off}%\" off on total"
  end

  def line_item_bmgnx_discount_message
    line_item_bmgnx_disc_msg = {}
    if (bmgnx_hash = PromotionPipeLine.bmgnx_hash).present? && (bmgnx_free_items = self.get_free_items_bmgnx(bmgnx_hash)).present?
      line_items.each do |line_item|
        if (item_hash = bmgnx_free_items[line_item.id]).present?
          line_item_bmgnx_disc_msg[line_item.id] = bmgnx_hash[:x] != 100 ? "You got #{bmgnx_hash[:x]} % off on #{item_hash[1]} product".pluralize(item_hash[1]) : (item_hash[1] == line_item.quantity ? "This item is free" : "Quantity #{item_hash[1]} of this item is free")
        end
      end
    end
    line_item_bmgnx_disc_msg
  end
  # Cost of all LineItems
  #
  # == Parameters:
  #   rate::
  #     Integer
  #
  # == Returns:
  # Integer
  # call with force_update = true if content in cart has changed due to modifications
  def item_total(rate, force_update=false)
    @item_total ||= {}
    @item_total[rate] = (!force_update && @item_total[rate]) || begin
      amount = 0
      self.line_items.each {|item| amount += item.total_currency(rate)}
      amount.round(CurrencyConvert.round_to)
    end
  end

  def addons_total(rate)
    (self.line_items.flatten.sum{ |item| (item.quantity * item.line_item_addons.flatten.sum{|addon| addon.snapshot_price_currency(rate)}).round(CurrencyConvert.round_to)}).round(CurrencyConvert.round_to)
  end

  # Cost of all LineItems for particular designer
  #
  # == Parameters:
  #   rate::
  #     Integer
  #   designer_id
  #     Integer
  #
  # == Returns:
  # Integer
  #
  def designer_item_total(rate, designer_id)
    self.line_items.joins(design: :designer).where(design:{designer_id: designer_id}).inject(0) do |sum, line_item|
      sum + (line_item.sor_available? ? 0 : line_item.total_currency_without_addons(rate, 'designer', false))
    end
  end


  def designer_item_scaled_total(rate, designer_id)
    self.line_items.joins(design: :designer).where(design:{designer_id: designer_id}).inject(0) do |sum, line_item|
      sum + (line_item.sor_available? ? 0 : line_item.total_currency_without_addons(rate, 'designer', true))
    end
  end

  # Cost of Cart
  #
  # == Parameters:
  #   rate::
  #     Integer
  #
  # == Returns:
  # Integer
  #
  def total_currency(rate, country_code = nil, shipping_country = nil, paypal = false, wallet_discounts_diduct = true)
    shipping = 0
    if shipping_country.to_s == 'India'
      total = self.item_total_with_discount(rate, country_code, paypal, wallet_discounts_diduct)
      essential_shipping, essential_total, all_essential = Order.get_essential_shipping(self.line_items) 
      if all_essential
        total += essential_shipping 
      else
        total += self.shipping_cost_currency(shipping_country, rate, total - essential_total) + essential_shipping
      end
    else
      total = self.item_total_with_discount(rate, country_code, paypal, wallet_discounts_diduct)
      total += self.shipping_cost_currency(shipping_country, rate, total) if shipping_country
    end

    total < 0 ? 0 : total.round(CurrencyConvert.round_to)
  end

  def item_total_with_discount(rate, country_code = nil, paypal = false, wallet_discounts_diduct = true)
    total = self.item_total(rate) - self.total_discounts_currency(rate)
    total -= self.wallet_discounts(country_code, rate, paypal) if wallet_discounts_diduct
    total
  end

  def get_total_shipping_cost(rate,country_code, shipping_country = nil, paypal=false, wallet_discounts_diduct = true)
    shipping = 0
    total = self.item_total_with_discount(rate, country_code, paypal, wallet_discounts_diduct)
    essential_shipping, essential_total, all_essential = Order.get_essential_shipping(self.line_items)
    if shipping_country.to_s == 'India'
      if all_essential
        shipping += essential_shipping 
      else
        shipping += self.shipping_cost_currency(shipping_country, rate, total - essential_total) + essential_shipping
      end
    else
      shipping =  self.shipping_cost_currency(shipping_country, rate, total) if shipping_country
    end
    return shipping
  end

  # Assigns coupon to cart
  #
  # == Parameters:
  #   coupon::
  #     Coupon Object
  #
  # == Returns:
  # Boolean
  #
  def assign_check_coupon?(coupon)
    self.coupon = coupon
    self.valid?
  end

  # Get discount for association
  #
  # == Parameters:
  #   association::
  #     Symbol
  #   designer_id::
  #     Integer
  #
  # == Returns:
  # Boolean
  #
  def discount_for(association, designer_id = nil, return_type = true)
    value = 0
    if self.coupon.present?
      case association
      when :order
        value = self.discount if self.coupon.designer_id.blank?
      when :designer_order
        value = self.discount(return_type) if self.coupon.designer_id == designer_id
      end
    end
    value
  end

  # Check LineItemAddon payable to mirraw
  # Group items by designer
  #
  # == Returns:
  # Boolean
  #
  def mirraw_payable_addons?
    LineItemAddon.joins(:line_item).where(line_item: {cart_id: self.id},
      snapshot_payable_to: 'mirraw').exists?
  end

  # Get total weight of items
  #
  # == Returns:
  # Integer
  #
  def weight
    value = 0
    self.line_items.each do |line_item|
      value += line_item.weight
    end  
    value
  end

  def exclude_shipping_item_total_without_addons(conversion_rate = 1)
    total = 0
    self.line_items.each do |item|
      if ![1,2,3].include?(item.buy_get_free) && (item.design.categories.pluck(:id) & EXCLUDE_FREE_SHIPPING_CATEGORIES).blank?
        total += (item.snapshot_price_currency(conversion_rate)).round(2) * item.quantity
      end
    end
    total.round(2)
  end

  def rakhi_pre_order
    value = {}
    count, rakhi_all_schedule = 0, 0
    self.line_items.each do |item|
      rakhi_all_schedule += 1 if item.note.present? && item.note.match('Rakhi Schedule Delivery')
      count += 1
    end
    if count == rakhi_all_schedule
      value['rakhi_all_schedule'] = true
    elsif rakhi_all_schedule != 0 && count > rakhi_all_schedule
      value['rakhi_with_other_designs'] = true
    end
    value
  end

  def stripe_currency_check(country_code)
    currency_code =CurrencyConvert.currency_convert_cache_by_country_code(country_code).symbol
    Order::STRIPE_ALLOWED_CURRENCIES.include?(currency_code.downcase)
  end

  # Provides payment options for cart
  #
  # == Parameters:
  #   params::
  #     Hash - containing shipping and billing Hash with pincode, country
  #   rate::
  #     Integer
  #
  # == Returns:
  # Hash
  #

  def payment_options_available(params, app_source, rate, country_code, app_version=nil, sub_app = nil)
    options = {}
    valid_phone = get_mobile_num(Address.find_by_id(params[:billing][:id]).try(:phone))
    positions = JSON.parse(SystemConstant.get('PAYMENT_OPTIONS_POSITIONS'))
    payu_hashes = user.payu_hashes
    stripe = render_stripe_option?(params, country_code, app_source, app_version)
    international_country = is_international_country?(country_code)
    options[Order::PAYPAL] = {additional_charge: 0,
      value: Order::PAYPAL,
      available: international_country && show_paypal_bt_for_version(app_source, app_version, sub_app), #|| (params[:billing][:country] != 'India' ),
      info: 'You will be redirect to PayPal page.',
      position: positions['paypal']
    }

  
    options[Order::BANK_DEPOSIT] = {additional_charge: 0,
      value: Order::BANK_DEPOSIT,
      available: (international_country && INT_BANK_DEPOSIT_PAYMENT == 'true'),
      info: 'Payment must be made to the account within 3 days of placing the order. We shall email you bank details.',
      position: positions['bank_deposit']
    }

    options['Credit / Debit Card'] = {additional_charge: 0,
      value: Order::PAYMENT_GATEWAY,
      available: !international_country,
      info: 'You will be redirected to PayU page for payment.',
      position: positions['credit_debit_card']
    }.merge!(payu_hashes)

    options['Payu Money'] = {additional_charge: 0,
      value: Order::PAYU_MONEY,
      available: !international_country,
      info: 'You will be redirected to PayU page for payment.',
      position: positions['payu_money']
    }

    options['Net Banking'] = {additional_charge: 0,
      value: Order::PAYMENT_GATEWAY,
      available: !international_country,
      position: positions['net_banking'],
      info: 'Please select your bank from the list.'
    }.merge!(payu_hashes)

    options['Credit Card'] = {additional_charge: 0,
      value: Order::PAYMENT_GATEWAY,
      available: international_country,
      is_stripe: stripe,
      position: positions['credit_card'],
      info:
        if stripe
          'You will be redirected to Secure Payment Gateway for payment.'
        else
          'You will be redirected to PayPal page to complete the payment.'
        end      
    }

    total_cod_charges = ((Order.domestic_cod_charge(params[:shipping][:country],params[:billing][:pincode], self) || 0) / rate).round(2)
    options[Order::COD] = {additional_charge: total_cod_charges,
      value: Order::COD,
      position: positions['cod'],
      info: information(app_source,valid_phone),
      verify_otp: wallet_discount_applied? || !(COD_OTP_DISABLED || otp_verified?(app_source)),
      phone_number_valid: wallet_discount_applied? ? 0 : valid_phone,
      available:
        if (params[:shipping][:country] == params[:billing][:country]) && allowed_cod_country(params[:shipping][:country]) && !international_country
          cod?(total_cod_charges,params[:shipping][:country].to_s,params[:billing][:pincode], rate)
        else
          false
        end
    }

    options[Order::CBD] = {additional_charge: 0,
      value: Order::CBD,
      position: positions['cbd'],
      freshdesk_link: FRESHDESK_LINK,
      info: 'Your product will be dispatched only after the payment has been collected by gharpay executive.',
      available:
        if !international_country
          false #cbd?(params[:billing][:pincode])
        else
          false
        end
    }

    options[Order::PAYTM] = {additional_charge: 0,
      value: Order::PAYTM,
      available: !PAY_WITH_PAYTM_DISABLED && !international_country && app_source != 'ios',
      position: positions['paytm'],
      info: 'You will be redirected to Paytm Page for payment completion.'
    }
    options
  end

  def render_stripe_option?(params, country_code, app_source, app_version)
    key = app_source.split('-')[0]
    unless ((params[:billing][:country] != 'India') && (rand(1..Rails.configuration.stripe[:redirect_to_stripe].to_i) == 1) && stripe_currency_check(country_code)) 
      return false
    end
    unless (app_version.present? && app_version.to_s >= Rails.configuration.stripe[:stripe_supported_versions][key])
      return false
    end
    return true
  end

  def show_paypal_bt_for_version(app_source=nil, app_version=nil, sub_app=nil)
    if app_source
      # for premium sub app, from v1.0.0 onwards braintree is accounted for but since the key is maintained as sub_app directly in db, need to add extra check for this #
      if sub_app and sub_app.downcase.include? "premium"
        return true
      end
      main_app_source = app_source.split('-')[0]
      sub_app = app_source.split('-')[1] ? true : false
      key = main_app_source
      key = key + "_sub_app" if sub_app
      return app_version.to_s >= PAYPAL_BRAINTREE_MINIMUM_VERSION[key].to_s
    end
  end

  def otp_verified?(app_source)
    (app_source.downcase.include?('android')) && user.present? && (user.orders.exists?(state: USER_CONFIRMED_ORDER_STATE, billing_phone: (user.addresses.default_address.first.try(:phone))))
  end

  def get_wallet_usable_msg
    unless (referral_amt = self.wallet.try(:referral_amount).to_f) > 0.0
      "You can avail at Max '#{Wallet.usage_percent}%' off on the Sub total"
    else
      "'#{get_symbol_from(self.wallet.currency_convert.hex_symbol)} #{referral_amt}' has been deducted from your referral wallet"
    end
  end

  def cart_has_bmgn_products?
    line_items.where(buy_get_free: 1).present? && PromotionPipeLine.bmgnx_hash.present?
  end

  def shipping_cost(country, rate, final_price = nil)
    value = 0
    if coupon.present? && coupon.is_shipping? && total_currency(1) >= coupon.min_amount
      value
    elsif country.to_s.downcase != 'india'
      value = get_int_shipping_cost_line_item_specific(rate, country)
    elsif final_price.to_f > 0
      value = (DOMESTIC_SHIPPING_CHARGES.find {|k,v| final_price < k.to_i}.try(:last) || 0).to_f / rate.to_f
    end
    value
  end

  def get_int_shipping_cost_line_item_specific(rate, country)
    # This Defination return Shipping Cost on Basis of Line Item
    excluded_weight = 0
    total_weight = self.weight
    excluded_weight += get_excluded_item_weight if shipping_categories_available?
    excluded_weight += get_bmgn_product_weight if cart_has_bmgn_products?
    if !self.free_shipping_available?(rate, country)
      value = (Order.shipping_cost_for(total_weight, country) / rate)
    else
      value = (Order.shipping_cost_for(excluded_weight, country) / rate)
    end
    return value
  end

  def get_bmgn_product_weight
    value = 0
    self.line_items.each do |line_item|
      value += line_item.weight if [1,2,3].include?(line_item.buy_get_free)
    end    
    value
  end

  def get_excluded_item_weight
    value = 0
    self.line_items.each do |line_item|
      value += line_item.weight if (line_item.design.categories.pluck(:id) & EXCLUDE_FREE_SHIPPING_CATEGORIES).present?
    end    
    value
  end

  def shipping_categories_available?
    skip_free_shipping = self.line_items.any?{|line_item| (line_item.design.categories.pluck(:id) & EXCLUDE_FREE_SHIPPING_CATEGORIES).present?}
  end

  def shipping_cost_currency(country, rate,final_price = nil)
    final_price = self.item_total_with_discount(1, @country_code) if country == 'India' && !final_price.present?
    self.shipping_cost(country, rate, final_price).round(CurrencyConvert.round_to(default: 2))
  end

  def get_shipping_message(shipping_country, shipping_city=nil)
    delivery_time, show_rts_msg, available_in_warehouse = self.get_delivery_time(shipping_country, shipping_city)
    shipping_message = delivery_time ? "* Get your order delivered within #{delivery_time} days. \n" : ""
    shipping_message << "* Shipping charge may change subject to your shipping Address."
    shipping_message << " \n* The products in your cart contains both Ready To Ship and Non - Ready To Ship products. Please check the modified delivery date and proceed." if show_rts_msg
    shipping_message
  end
  

  def payment_details(params, rate, min_cart_value, app_source, country_code = nil, app_version = nil, sub_app = nil)
    Address.shipping_country = params[:shipping][:country]
    values = {
      item_count: self.total_items,
      item_total: self.item_total(rate),
      items_total_without_addons: self.items_total_without_addons(rate),
      discount: self.total_discounts_currency(rate),
      wallet_discounts: self.wallet_details(country_code),
      wallet_money: self.wallet_money(country_code),
      payment_options_key: 'pay_type',
      payment_options: app_source != 'web' ? payment_options_available(params, app_source, rate, country_code, app_version, sub_app) : {},
      conditional_offers: self.conditional_offers(rate, params[:shipping][:country]),
      addon_charges: self.addons_total(rate)
    }
    # -------for shipping_total value
    values[:total] = (values[:item_total] - values[:discount])
    if params[:shipping][:country] == 'India' && country_code.to_s == 'IN'
      essential_shipping_charges, essential_total, all_essential = Order.get_essential_shipping(self.line_items)
      if all_essential
        values[:shipping_total] = essential_shipping_charges.to_f
      else
        final_price = self.item_total_with_discount(1, 'India' )
        values[:shipping_total] = self.shipping_cost_currency('India', 1, (final_price - essential_total) ).to_f + essential_shipping_charges.to_f
      end
    else
      values[:shipping_total] =  self.shipping_cost_currency(params[:shipping][:country], rate, values[:total] ).to_f
    end
    # ------
    values[:total] += values[:shipping_total] 
    values[:min_cart_value_criteria_failed] = check_for_min_cart_condition?(MIN_CART_VALUE_ON_HEADER[app_source], country_code, Address.shipping_country) && (values[:item_total] * rate).to_i < min_cart_value
    if ((order = user.orders.where(state: USER_CONFIRMED_ORDER_STATE).last).present? && (default = order.try(:pay_type)))
      if default == Order::PAYMENT_GATEWAY
        values[:default] = params[:billing].try(:[], :country) != 'India' ? 'Credit Card' : 'Credit / Debit Card'
      elsif (value = values[:payment_options][default]).present? && value[:available]
        values[:default] = default
      end
    end
    values[:prepaid_shipping_promo] = params[:billing].try(:[], :country) == 'India' && DOMESTIC_PREPAID_SHIPPING_PROMOTION
    values[:prepaid_discount] = params[:billing].try(:[], :country) == 'India' ? (self.prepaid_discount).to_i : 0
    values[:prepaid_percent] = params[:billing].try(:[], :country) == 'India' ? self.prepaid_percent.to_i : 0
    values[:total_with_prepaid_discount] = values[:total] - values[:prepaid_discount]
    if values[:prepaid_shipping_promo]
      values[:total_with_prepaid_discount] -= values[:shipping_total]
    end
    values[:mastercard_discount] = params[:billing].try(:[], :country) == 'India' ? (self.mastercard_discount(country_code, rate)).to_i : 0
    values[:mastercard_discount_percent] = Promotion.mastercard_discount_percent.to_i if values[:mastercard_discount] > 0
    if values[:mastercard_discount] > 0
      values[:total_with_mastercard_discount] = values[:prepaid_shipping_promo] ? values[:total] - values[:mastercard_discount] - values[:shipping_total] : values[:total] - values[:mastercard_discount]
    end
    total_in_inr = values[:total] * rate
    values[:show_mail_us] = total_in_inr >= MIN_CART_VALUE_TO_SHOW_MAIL_US.to_i ? true : false
    values[:juspay_enable] = country_code == 'IN' && params[:billing].try(:[], :country) == 'India' && rand(1..ENV['JUSPAY_EXPRESS_CHECKOUT'].to_i) == 1
    juspay_options = values[:juspay_enable] ? PaymentOption.fetch_juspay_options(app_source, params[:enable_cod]).to_a : []
    updated_juspay_options = if juspay_options.present? && app_source != 'web'
      juspay_options.reject{|opt| opt.gateway_name == Order::COD && !values[:payment_options][Order::COD][:available]}
    else
      []
    end
    values[:juspay_payment_opt] = updated_juspay_options
    values[:juspay_saved_cards] = if values[:juspay_enable] && (user = self.user).present? && user.juspay_customer.present?
      response_hash = user.get_saved_cards
      response_hash['cards']
    else
      []
    end
    if Design.country_code == 'IN' && Wallet.cashback_percent > 0
      values[:prepaid_promo_msg] = "Pay Online and get #{Wallet.cashback_percent}% Cashback on orders above #{Wallet.min_order_total_for_cashback}"
    end
    tax_rate, tax_amount, total, tax_enable = get_total_with_tax(country_code, values[:total])
    values[:tax_rate] = tax_rate
    values[:tax_amount] = tax_amount
    values[:total] = total
    values[:tax_enable] = tax_enable
    values[:total] =  (values[:total] - values[:wallet_discounts][:total]).round(CurrencyConvert.round_to)
    values[:total] = 0 if values[:total] < 0
    return values
  end

  def check_for_min_cart_condition?(header, country_code, shipping_country)
    (header.present? && header['enabled'] && header['countries'].split(',').include?(country_code.to_s)) || shipping_country.try(:downcase).to_s != "india"
  end

  def wallet_details(country_code)
    country_code ||= Design.country_code
    referral_amount = wallet_referral_amount(country_code)
    return_amount = wallet_return_amount(country_code)
    total = referral_amount + return_amount
    {
      referral_amount: referral_amount,
      return_amount: return_amount,
      total: total
    }
  end
  # Total items 'quantity'
  #
  # == Returns:
  # Integer
  #
  def total_items
    line_items.loaded? ? line_items.to_a.sum(&:quantity) : line_items.sum(:quantity)
  end

  # Find and group items by designer
  #
  # == Parameters:
  #   id::
  #     Integer
  #
  # == Returns:
  # Hash
  #
  def self.designer_values(id)
    if (cart = Cart.find_by_id(id))
      cart.designer_values
    else
      {}
    end
  end

  def designer_values
    params = {}
    ActiveRecord::Associations::Preloader.new.preload(self.line_items,[:line_item_addons,design: :categories])
    if (bmgnx_hash = PromotionPipeLine.bmgnx_hash).present?
      bmgnx_offer_line_items_hash = get_free_items_bmgnx(bmgnx_hash)
    end
    self.line_items.each do |line_item|
      designer_id = line_item.design.designer_id
      if bmgnx_hash.present? && (item_hash = bmgnx_offer_line_items_hash[line_item.id]).present? 
        line_item.add_note("| Discount items (B#{bmgnx_hash[:m]}G#{bmgnx_hash[:n]}): #{item_hash[1]} At #{bmgnx_hash[:x]} % off |", ignore_if_exists: true)
      end
      if (d = line_item.design).flash_deals_available?
        active_promotions = PromotionPipeLine.active_promotions
        percent = d.flash_deals_price_percent(active_promotions)
        line_item.add_note("| Flash Deals (#{percent}) % off |", ignore_if_exists: true)
      end
      params[designer_id] = {line_items: [],
        discount: self.discount_for(:designer_order, designer_id),
        scaled_discount: self.discount_for(:designer_order, designer_id,false)
      } if params[designer_id].blank?
      line_item.assign_synced_attributes
      line_item.vendor_selling_price = (line_item.variant || line_item.design).get_vendor_selling_amount
      params[designer_id][:line_items] << line_item
    end
    params
  end

 def self.get_essential_designers_total(line_items)
    designer_hash={}
    line_items.each do |li|
        de = li.design
        d = de.designer
        designer_hash[d.id] = {sum: 0, essential: false}  if !designer_hash.key?(d.id)
        designer_hash[d.id][:sum]+=(li.snapshot_price).round(2) * li.quantity
        designer_hash[d.id][:essential] = true if ESSENTIAL_DESIGNERS["designer_ids"].map(&:to_i).include?(d.id)    
    end
    return designer_hash
  end

  def items_total_without_addons(conversion_rate = 1)
    total = 0
    self.line_items.each do |item|
      total += (item.snapshot_price_currency(conversion_rate)).round(2) * item.quantity
    end
    total.round(2)
  end

  def free_stiching_coupon?
    if coupon.present?
      coupon.is_stitching? && coupon.active? && items_total_without_addons(1) >= coupon.min_amount
    end
  end

  def conditional_offers(rate, country)
    {shipping: self.shipping_offers(rate, country)}
  end

  # Check COD available
  #
  # == Parameters:
  #   pincode::
  #     String
  #
  # == Returns:
  # Boolean
  #
  def cod?(charges,country,pincode,rate = 1)
    if allowed_cod_country(country) && country.try(:downcase) != 'india' 
      return true
    elsif !allowed_cod_country(country) || (country.try(:downcase) == 'india' && pincode.length != 6) || self.mirraw_payable_addons? || total_currency(rate,Design.country_code) < (MIN_CART_VAL_FOR_COD.to_f/rate) || !charges
      return false
    else
      pincode_to_int = pincode.to_i
      Courier.cod_designs(line_items.loaded? ? line_items.collect(&:design_id) : line_items.pluck(:design_id), allowed_cod_country(country), (country.downcase == 'india' ? pincode : ''))[:cod] && !LOCKDOWN_COD_DISABLE_PINCODES.include?(pincode_to_int)
    end
  end

  def allowed_cod_designers_for_uae
    count_designs_with_designers_cod_enable = 0
    count_designs_with_designers_cod_not_enable = 0
    self.line_items.each { |item|
      count_designs_with_designers_cod_enable +=1 if ENABLE_COD_FOR_DESIGNERS_FOR_UAE.include?(item.design.designer_id)
      count_designs_with_designers_cod_not_enable +=1 if !ENABLE_COD_FOR_DESIGNERS_FOR_UAE.include?(item.design.designer_id)
    }
    if count_designs_with_designers_cod_not_enable > 0
      return false
    else
      return true
    end
  end

  def allowed_cod_country(country)
    downcased_country = country.try(:downcase) 
    if downcased_country == 'india'
      return true
    else
      return ENABLE_COD_COUNTRIES.include?(downcased_country) && (self.all_products_in_warehouse? || allowed_cod_designers_for_uae) 
    end
  end

  def cbd?(pincode)
    Courier.cbd_available?(pincode)
  end

  def quick_cod_cart(params, app_source, rate)
    address = Address.find_by_id(params[:address_id]) if params[:address_id].present?
    {
      cod_designs_response: Courier.cod_designs(line_items.pluck(:design_id), allowed_cod_country(address.try(:country).to_s), (address.try(:country).to_s.downcase == 'india' ? params[:pincode] : '')),
      total_cod_charges:((( domestic_cod_charges = Order.domestic_cod_charge(address.try(:country).to_s,params[:pincode], self)) || 0) / rate).round(2),
      otp_verified: (COD_OTP_DISABLED || otp_verified?(app_source)),
      cod_charges_available: !(params[:pincode].length != 6 || !domestic_cod_charges || mirraw_payable_addons?),
      valid_phone: (get_mobile_num(address.try(:phone)))
    }
  end

  def self.cod_charge_currency(rate)
    (COD_CHARGE / rate).round(2)
  end

  def generate_hash_value!
    self.update_attributes(hash1: SecureRandom.hex(16)) unless self.hash1?
  end

  def generate_otp(phone, resend)
    phone = get_mobile_num(phone)
    if phone && phone != 0 && phone.length == 12
      if otp == 'verified' || otp.blank? || resend != 'true'
        return false unless update_attribute(:otp, '%05d' % SecureRandom.random_number(100000))
      end
      template = "#{otp} is your verification code. Use this code to confirm your COD order. Thanks, Mirraw.com."
      template = URI.encode(template)
      sms_api_params = SMS_API.sub('{phone}', phone).sub('{template}', template)
      return HTTParty.get(sms_api_params).parsed_response.include?('MsgID')
    end
    false
  end

  def add_product(design, app_source='Mobile')
    current_item = self.line_items.select {|item| item.design_id == design.id}
    if current_item.present?
      current_item = current_item[0]
      message = "Product has already been added to the cart."
    else
      current_item = LineItem.new(design: design, snapshot_price: design.effective_price, scaling_factor: design.get_scale, vendor_selling_price: design.get_vendor_selling_amount, app_source: app_source)
      line_items << current_item
      message = current_item.design.title + " has been added to cart."
    end
    [current_item, message]
  end 

  def remove_existing_invalid_coupon
    if self.invalid? && self.errors[:coupon].present?
      self.update_attribute(:coupon_id, nil)
    end
  end

  def remove_existing_referral(current_user)
    self.remove_from_wallet(:referral_amount) if !current_user && self.wallet && self.wallet.referral_amount.nonzero?
  end

  def remove_existing_return
    self.remove_from_wallet(:return_amount) if self.wallet && self.wallet.return_amount.nonzero?
  end

  def check_out_of_stock?(country = nil)
    self.line_items.includes(:variant, design: :designer).each do |item|
      return true if ['banned', 'on_hold', 'inactive'].include?(item.design.designer.state_machine)
      klass = item.variant || item.design
      if country.try(:downcase) == 'india' && item.quantity > klass.designer_quantity
        return true
      elsif item.quantity > klass.quantity
        return true
      end
    end
    return false
  end

  def apply_from_wallet(type, wallet, rate, country_code, shipping_country = nil)
    total = self.total_currency(rate, country_code, shipping_country, false, false)
    _ , _ , total, _ = self.get_total_with_tax(country_code, total)
    cart_wallet = self.wallet
    if total > 0
      if type == :referral_amount
        applied_discount = wallet.usable_referral_amount(total, 2)
        self.apply_discount(wallet, cart_wallet, applied_discount, type)
      end

      if type == :return_amount || type == :referral_amount && cart_wallet.try(:return_amount).to_f > 0
        available_return_amount = wallet.return_amount
        remaining_total = total - cart_wallet.try(:referral_amount).to_f
        applied_discount = remaining_total > available_return_amount ? available_return_amount : remaining_total
        self.apply_discount(wallet, cart_wallet, applied_discount, :return_amount)
      end
    else
      wallet.errors.add(:error, 'Not Applicable')
    end
  end

  def apply_discount(wallet, cart_wallet, applied_discount, type)
    if cart_wallet.present?
      cart_wallet.update_attributes(type => applied_discount, currency_convert_id: wallet.currency_convert_id)
    elsif cart_wallet.nil?
      cart_wallet = Wallet.create(type => applied_discount, currency_convert_id: wallet.currency_convert_id)
      self.update_attribute(:wallet_id, cart_wallet.id)
    else
      wallet.errors.add(:error, 'Not Applicable')
    end
  end

  def remove_from_wallet(type)
    wallet = self.wallet
    wallet.update_column(type, 0)
    self.delete_wallet if wallet.referral_amount.zero? && type.to_s == 'return_amount'
  end

  def delete_wallet
    Wallet.where(id: self.wallet_id).first.try(:destroy)
    self.update_attribute(:wallet_id, nil)
  end

  def wallet_money(country_code)
    wallet = self.user.wallet
    {
      available: (wallet.currency_convert.country_code == country_code) && wallet.return_amount > 0,
      amount: wallet.return_amount
    }
  end

  def send_notification(app_version, app_source, account_id = nil)
    app_versions = ALLOWED_APP_VERSIONS[10..-1] | ALLOWED_IOS_APP_VERSIONS[5..-1]
    if self.notified != 'T' && (self.notified_at.nil? || self.notified_at < 1.days.ago) && (account_id || app_versions.include?(app_version))
      self.update_attribute(:notified, 'T')
      Cart.sidekiq_delay_until(DELAYED_MINUTES.minutes.from_now).send_notification(self.id, app_version, app_source, account_id)
    end
  end

  def send_cart_timer_notification(app_version, app_source, device_id, api_account_id, fcm_registration_token)
    app_versions = ALLOWED_APP_VERSIONS[10..-1] | ALLOWED_IOS_APP_VERSIONS[5..-1]
    if device_id && app_versions.include?(app_version)
      Cart.sidekiq_delay_until(1.minutes.from_now).send_cart_timer_notification(self.id, app_version, app_source, device_id, api_account_id, fcm_registration_token)
    end
  end

  def additional_discount_note(rate, symbol)
    msg = nil
    percent_price = Promotion.additional_discount_percent(Design.country_code)
    percent_price[0].each_with_index do |percent, index|
      amount_on = ApplicationController.helpers.get_price_in_currency(percent_price[1][index].to_i, rate)
      bmgn_discounts = ApplicationController.helpers.get_price_in_currency(bmgnx_discounts, rate)
      cart_total = item_total(rate) - bmgn_discounts
      if !self.coupon.present?
        if cart_total <= amount_on
          return "Save #{percent.to_i}% off on purchase above #{get_price_with_symbol(amount_on, symbol)}"
        elsif amount_on > 0 && cart_total > amount_on
          msg = "Extra #{percent.to_i}% discount is applied on your cart."
        end
      end
    end
    msg
  end

  def promotional_discount_offer(rate, actual_country, symbol)
    if (offers = self.conditional_offers(rate, actual_country)).present? && offers[:shipping].present?
      if offers[:shipping][:available]
        "You are eligible for free shipping"
      else
        "Free Shipping on purchase #{offers[:shipping][:on_category]} above #{get_price_with_symbol(offers[:shipping][:free_shipping_at], symbol)}"
      end
    end
  end

  def has_design?(design_id)
    line_items.loaded? ? line_items.any?{|li| li.design_id == design_id} : line_items.exists?(design_id: design_id)
  end

  def get_messages(rate, symbol, country_code, actual_country, app_source, user, min_cart_value, app_version, shipping_country=nil)
    msgs = []
    imp_msg= {}
    user_wallet = user.try(:wallet)
    if Wallet.cashback_percent > 0 && REFERRALS_ACTIVE_COUNTRIES.include?(country_code) && (amount_to_credit = Wallet.cashback_for_amount(total_currency(rate, country_code, (shipping_country || actual_country))*rate))> 0
      msgs << "Get cashback worth #{get_symbol_from(symbol)} #{get_price_in_currency(amount_to_credit, rate).round(2)} in your Mirraw wallet by placing this order."
    end
    if (bmgnx_hash = PromotionPipeLine.bmgnx_hash).present?
      msgs << get_bmgnx_notice(bmgnx_hash: bmgnx_hash)
    elsif (qpm_msg = self.next_qpm_msg).present?
      msgs << qpm_msg
    end
    msgs << additional_discount_note(rate, symbol)
    if country_code != 'IN' && Promotion.free_shipping_on_country?
      msgs << promotional_discount_offer(rate, actual_country, symbol)
    elsif country_code == 'IN' && COD_NOTE_MESSAGE != 'false'
      msgs << COD_NOTE_MESSAGE
    end
    if user_wallet.present? && user_wallet.return_amount > 0 && user_wallet.currency_convert.country_code == country_code
      msgs << "You have #{get_symbol_from(symbol)} #{user_wallet.return_amount} in your wallet! Place order and use it to get additional discount, only few hours left.."
    end
    if (c = coupon).present?
      msgs << "Applied Discount Coupon - #{c.code}"
    end
    if country_code == 'IN' && ESSENTIAL_DESIGNERS["total_below_x"] <= 0
      version_check = (app_version.present? && app_source.present?) ? DOMESTIC_PREPAID_SHIPPING_PROMOTION && app_version > PREPAID_PROMOTION_MINIMUM_VERSION[app_source] : false
      msg_change = version_check ? 'Cash on Delivery' : ''
      if version_check
        msgs << "Get FREE Shipping on Online Payment"
      end
      if DOMESTIC_SHIPPING_CHARGES.present? && DOMESTIC_SHIPPING_CHARGES.keys.last.to_i > 0
        msgs << "Free Shipping on #{msg_change} Orders above ₹ #{DOMESTIC_SHIPPING_CHARGES.keys.last.to_i}"
      else
        msgs << "Free Shipping on all Orders"
      end
    end
    if app_version.present? && app_source.present?
      if ((offer_msg = PrepaidPaymentPromotion.cart_message(country_code)).present? && app_version > PREPAID_PROMOTION_MINIMUM_VERSION[app_source])
        msgs << offer_msg
      end
    end
    if check_for_min_cart_condition?(MIN_CART_VALUE_ON_HEADER[app_source.downcase], country_code, actual_country) && ((cart_total = item_total(rate)) * rate).to_i < min_cart_value
      msg = "Minimum order value should be of #{get_symbol_from(symbol)} #{CurrencyConvert.to_currency(country_code, min_cart_value).round(2)}. Current order value is #{get_symbol_from(symbol)} #{cart_total} (shipping charges excluded)."
      if SHOW_IMP_CART_MSG != 'false' && app_version.present? && ( (app_source.include?('Android') && ALLOWED_APP_VERSIONS[45..-1].include?(app_version)) || (app_source.include?('iOS') && ALLOWED_IOS_APP_VERSIONS[11..-1].include?(app_version)) )
        imp_msg = {
                    msg_text: msg,
                    bg_color: "#ffff4444",
                    msg_color: "#FFFFFF",
                  }
      else
        msgs << msg
      end
    end
    if country_code != 'IN' && free_stitching_on_country?(country_code)
      msgs << free_stitching_text(country_code, rate, get_symbol_from(symbol))
    end
    return msgs, imp_msg
  end

  def show_applied_coupon
    "Applied Discount Coupon - #{coupon.code}" if coupon.present?
  end

  # delayed
  def self.send_notification(cart_id, app_version, app_source, account_id)
  end

  def amazon_data(cart_data={})
    xml= to_amazon_xml(cart_data)
    xml_base64 = Base64.strict_encode64(xml)
    sha1 = OpenSSL::Digest::SHA1.new
    signature = Base64.strict_encode64(OpenSSL::HMAC.digest(sha1, MirrawMobile::Application.config.pay_with_amazon[:secret_key], xml))
    "type:merchant-signed-order/aws-accesskey/1;order:#{xml_base64};signature:#{signature};aws-access-key-id:#{MirrawMobile::Application.config.pay_with_amazon[:aws_access_key]}"
  end

  def has_minimun_quantity
    minimum_quantity = PAY_WITH_AMAZON_MIN_QUANTITY.to_i
    line_items.all? do|line_item|
      (line_item.variant.try(:quantity) || line_item.design.quantity) > line_item.quantity + minimum_quantity
    end
  end

  def ready_to_ship_designs?
    line_items.all? { |item| item.design.ready_to_ship? && !item.paid_addons? }
  end

  def warehouse_available_designs?
    line_items.all? { |item| (item.variant.presence || item.design).sor_available? }
  end

  def all_products_in_warehouse?
    self.line_items.all? { |item| (item.variant.presence || item.design).sor_available? || (item.design.designer_id  == 12727) }
  end

  def get_delivery_time(shipping_country, shipping_city=nil)
    delivery_time, addon_delivery_time, line_items_rts, available_in_warehouse, designer_ids = 0, 0, [], [], []
    list = [0]
    self.line_items.each do |item|
      vendor = item.design.designer
      list << vendor.vacation_days_count
      designer_ids << item.design.designer_id
      if item.variant || item.line_item_addons.blank?
        in_warehouse = shipping_country != 'India' && (item.variant_id.present? ? item.variant.sor_available? : item.design.sor_available?)
        line_items_rts << in_warehouse
        available_in_warehouse << in_warehouse
      end
      designer_eta = item.design.designer_shipping_time(shipping_country, shipping_city, self.user)
      item.line_item_addons.each do |addon|
        prod_time  = addon.addon_type_value.prod_time
        design_eta = item.design.sor_available? ? 0 : designer_eta
        if addon.size_chart_id.present?
          if addon.open_addon?
            in_warehouse = item.design.size_in_warehouse?(quantity: item.quantity)
            line_items_rts << in_warehouse
            available_in_warehouse << in_warehouse
            design_eta = designer_eta if !in_warehouse
          elsif addon.standard_addon?
            if item.design.size_in_warehouse?(addon.size_chart.size_bucket_id, quantity: item.quantity)
              prod_time = 0
              line_items_rts << true
              available_in_warehouse << true
            elsif item.design.size_in_warehouse?(quantity: item.quantity)
              line_items_rts << false
              available_in_warehouse << true
            else
              line_items_rts << false
              available_in_warehouse << false
              design_eta = designer_eta
            end
          elsif addon.custom_addon?
            in_warehouse = item.design.size_in_warehouse?(quantity: item.quantity)
            line_items_rts << false
            available_in_warehouse << in_warehouse
            design_eta = designer_eta if !in_warehouse
          end
        elsif addon.standard_addon?
          line_items_rts << false
        end
        delivery_time = (design_eta + prod_time) if delivery_time < (design_eta + prod_time)
        addon_delivery_time = prod_time if addon_delivery_time < prod_time
      end
      delivery_time = designer_eta if delivery_time < designer_eta && item.line_item_addons.blank? && (shipping_country == 'India' || !item.design.sor_available?)
    end
    vacation_day_count = list.max
    if shipping_country != 'India' && (available_in_warehouse.uniq.length == 1 && available_in_warehouse.first)#self.warehouse_available_designs?
      delivery_time = (Country.country_wise_delivery_time(shipping_country) + addon_delivery_time).round.to_s
    elsif shipping_country != 'India' && (self.ready_to_ship_designs? && EXPRESS_DELIVERY.to_f >= 0)
      delivery_time = (READY_TO_SHIP_DESIGNS.to_i + delivery_time).round.to_s
    elsif shipping_country == 'India'
      delivery_time = if shipping_city.present?
        delivery_time.to_i
      elsif @actual_country == 'India'
        CITY_BASED_SHIP_TIME[:non_metro].to_i
      else 
        Country.country_wise_delivery_time('india').to_i + delivery_time.to_i
      end
      delivery_time.round.to_s
    else
      delivery_time = (Country.country_wise_delivery_time(shipping_country) + delivery_time).round.to_s
    end
    if is_essential_products(designer_ids.uniq) && shipping_country == 'India'
      delivery_time = ESSENTIAL_DESIGNERS['max_eta']
    end
    return [delivery_time, (line_items_rts.uniq.length > 1), (available_in_warehouse.uniq.length == 1 && available_in_warehouse.first)]
  end

  #Delayed task
  def self.send_cart_timer_notification(cart_id, app_version, app_source, device_id, api_account_id, fcm_registration_token)
  end

  def belongs_to_user?(current_user)
    self.user_id == current_user.try(:id)
  end

  def wallet_discount_applied?
    wallet_details(Design.country_code)[:total] > 0
  end

  def show_stitching_offer?
    line_items.includes(:design).each do |line_item|
      return true if Design::STITCHING_ALLOWED_DESIGNABLE_TYPES.include?(line_item.design.designable_type)
    end
    false
  end

  # clear already ordered items and create duplicates for them
  def refresh_ordered_items!
    if (ordered_items = line_items.select{|li| li.designer_order_id.present?}).present?
      self.skip_line_item_wallet_update = true
      line_items.delete(*ordered_items.collect(&:id))
      ordered_items.each do |item|
        new_line_item = item.dup
        new_line_item.line_item_addons << item.line_item_addons.collect(&:dup)
        new_line_item.designer_order_id = nil
        #remove line items even from memory, if not valid (as it does not get saved to database)
        line_items.delete(new_line_item) unless line_items << new_line_item
      end
      self.skip_line_item_wallet_update = nil
    end
  end

  def design_ids
    line_items.loaded? ? line_items.collect(&:design_id) : super
  end

  def details_for_gtm
    {cartProductIds: design_ids.join(',')}
  end

  def cart_addon_designs(country_code)
    design_ids = line_items.group_by(&:design_id)
    designable_types = line_items.map(&:design).map(&:designable_type).uniq 
    geo = country_code.downcase == 'in' ? 'domestic' : 'international'
    CART_ADDON[geo].to_h.slice(*designable_types).map do |designable_type, design_ids|
      Rails.cache.fetch("#{designable_type}_designs_for_cart_addon",expires_in: 24.hours) do
        [designable_type, Design.where(id: design_ids, state: 'in_stock').uniq.to_a]
      end
    end.map do |designable_type, designs|
      [designable_type, designs.map do |design|
        design_ids[design.id].try(:first) || design
      end]
    end.to_h
  end

  def cart_addon_item(country_code)
    @cart_addon_item ||= cart_addon_designs(country_code).values.flatten.select do |item|
      item.is_a?(LineItem)
    end
  end

  def add_more_items_value(country_code, rate)
    total = country_code == 'IN' ? DOMESTIC_SHIPPING_CHARGES.keys.last.to_i - self.total_currency(rate, country_code) : 0
    total = 0 if total < 0
    total
  end

  def pincode_servicable_for_category?(pincode)
    if line_items.any? {|lt| (PINCODE_BASED_CATEGORY_DELIVERY['categories'] & lt.design.categories.pluck(:name)).present?}
      return PINCODE_BASED_CATEGORY_DELIVERY['pincodes'].include? pincode.to_s
    else
      return true
    end
  end

  private

  def is_essential_products(d_ids)
    check_essential_lenght(d_ids) && is_all_essential_designers(d_ids)
  end

  def check_essential_lenght(d_ids)
    ESSENTIAL_DESIGNERS['designer_ids'].length >= d_ids.length
  end

  def is_all_essential_designers(d_ids)
    (ESSENTIAL_DESIGNERS['designer_ids'] & d_ids.map(&:to_s)).length == d_ids.length
  end

  def information(app_source, valid_phone)
    if wallet_discount_applied?
      'Wallet cannot be used with Cash On Delivery. Use other payment option to utilize wallet amount.'
    elsif COD_OTP_DISABLED || otp_verified?(app_source)
      ''
    elsif valid_phone == 0
      "Mobile Number doesn't seem valid."
    else
      "You need to verify your mobile number for Cash on Delivery Orders. An OTP will be sent on +#{valid_phone}." << "\nTo change the phone number, please edit your address below and click 'SHIP HERE' after saving."
    end
  end

  def set_default_user_type
    self.user_type||='User' if user_id.present?
  end

  def to_amazon_xml(cart_data)
    to_hash(cart_data).to_xml(root: 'Order',indent:0,skip_types: true).gsub!('<Order',"<Order xmlns='http://payments.amazon.com/checkout/2009-05-15/'")
  end

  def to_hash(cart_data)
    order={
      Cart: { Items: []},
      Promotions: [],
      ReturnUrl: MirrawMobile::Application.config.pay_with_amazon[:return_url],
      CancelUrl: MirrawMobile::Application.config.pay_with_amazon[:cancel_url]
    }
    order[:Cart][:Items] = line_items.collect(&:to_amazon_hash)
    if (discount = total_discounts) > 0
      order[:Cart][:CartPromotionId] = "Mirraw discount"
      order[:Promotions]<<{
        PromotionId:"Mirraw discount",
        Benefit:{
          FixedAmountDiscount:{
            Amount: discount,
            CurrencyCode: 'INR'
          }
        }
      }
    end
    order[:Cart][:CartCustomData]={cart_id: id,total_amount: total_currency(1)}.merge!(cart_data)
    order
  end

  # Returns mobile number given differnet valid representation returns 0 for invalid representation
  def get_mobile_num(phone)
    if phone.present?
      phone.tr!(' +-', '')
      if phone.match(/^[\d]+$/)
        case phone.length
        when 10
          return '91' + phone
        when 11
          return '91' + phone[1..-1] if phone[0] == '0'
        when 12
          return phone if phone[0..1] == '91'
        end
      end
    end
    0
  end

end
