class Review < ActiveRecord::Base
  normalize_attribute :review, :with => [ :strip, :blank ]

  validates :rating, presence:true, :inclusion => 1..5
  validates_presence_of :design_id, :designer_id, :user_id, unless: Proc.new{|review| review.site_review == true}

  belongs_to :user
  belongs_to :designer
  belongs_to :design, touch: true
  belongs_to :order
  has_many :survey_answers, :as => :surveyable
  has_and_belongs_to_many :orders

  delegate :full_name, to: :user, prefix: true, allow_nil: true

  after_save :update_design_and_designer_ratings, unless: Proc.new{|review| review.site_review == true}
  # after_save :add_negative_review_event
  before_create :validate_purchase

  scope :approved, -> { where(approved: true) }
  scope :site, -> {where(site_review: true)}
  scope :nps, -> { where('order_id > 0') }
  scope :product, -> {where(site_review: [false, nil])}
  scope :comments, -> {where('review is not null and user_id is not null')}
  scope :latest, -> {order('created_at  desc').limit(5)}
  scope :random_postive_rating, -> { order('created_at  desc').limit(3) }
  scope :with_enabled_designer_reviews, -> { joins(:designer).where(designers: { enable_reviews: true }) }

  class << self
    # Return the rating and review of a design
    #
    def design_ratings(design_id, user_id)
      design_reviews = Review.includes(:user, :designer).approved.where(design_id: design_id)
                             .joins(:designer).where(designers: { enable_reviews: true })
      rating = {5 => 0, 4 => 0, 3 => 0, 2 => 0, 1 => 0}.merge(design_reviews.group(:rating).count)
      reviews = design_reviews.where.not(review: nil, user_id: nil)
      reviews = reviews.where.not(user_id: user_id) unless user_id.blank?
      reviews = reviews.order('updated_at DESC')
      [rating, reviews]
    end
  end

  # update the review column for design and designer after the insertion or updation of review
  #
  def update_design_and_designer_ratings
    self.design.update_avg_ratings
    self.designer.update_avg_ratings
  end

  # Validates that the design has been puchased by user
  #
  def check_if_user_bought_design
    design_ids = LineItem.where(cart_id: self.user.orders.pluck(:cart_id)).pluck(:design_id).compact
    self.errors[:Invalid] <<'Not a valid user to review this design' unless design_ids.include?self.design_id
  end

  def reviewers_name
    user_full_name
  end

  # def add_negative_review_event
  #   self.class.delay.add_negative_review_event(id)
  # end

  # def self.add_negative_review_event(*ids)

  # end


  def self.new_review(line_item_id, review_text: nil, rating: nil)
  end


  private

  def validate_purchase
    # Check if designer has reviews enabled
    unless designer&.enable_reviews
      errors.add(:base, 'Reviews are not enabled for this designer.')
      return false
    end

    if (order_id = user.can_review(design_id)) > 0
      self.order_id = order_id unless self.order_id.present?
      true
    else
      errors.add(:base, 'Cannot review this product.')
      false
    end
  end
end
