
# == Schema Information
#
# Table name: designs
#
#  id                       :integer          not null, primary key
#  description              :text
#  title                    :string(255)
#  price                    :integer
#  designer_id              :integer
#  eta                      :integer
#  tmp_category             :integer
#  quantity                 :integer          default(1)
#  color                    :string(255)
#  specification            :text
#  created_at               :datetime
#  updated_at               :datetime
#  published                :boolean          default(FALSE)
#  grade                    :integer          default(40)
#  discount_percent         :integer
#  discount_price           :integer
#  cached_slug              :string(255)
#  design_code              :string(255)
#  min_wholesale_quantity   :integer
#  min_wholesale_price      :integer
#  weight                   :integer
#  retail                   :boolean
#  sell_count               :integer
#  follow_master_addon      :boolean
#  state                    :string(255)
#  last_in_stock            :datetime
#  last_sold_out            :datetime
#  last_seller_out_of_stock :datetime
#  last_blocked             :datetime
#  last_banned              :datetime
#  deleted_on               :datetime
#  reject_on                :datetime
#  review_on                :datetime
#  notes                    :text
#  return_count             :integer          default(0)
#  designable_type          :string(255)
#  designable_id            :integer
#  clicks                   :integer
#  conversion_rate          :decimal(8, 2)
#  coupon_id                :integer
#  in_catalog               :boolean          default(TRUE), not null
#  package_details          :string(255)
#  region                   :string(255)
#  pattern                  :string(255)
#  embellish                :string(255)
#  accessories              :text
#  in_catalog_one           :integer
#

class Design < ActiveRecord::Base
  extend FriendlyId
  include SidekiqHandleAsynchronous
  has_many :bestsellers
  acts_as_taggable
  acts_as_taggable_on :collections
  acts_as_taggable_on :domestic_tags, :international_tags
  acts_as_taggable_on :catalogues
  acts_as_followable

  belongs_to :designer
  belongs_to :designable, :polymorphic => true
  belongs_to :design_group
  has_many :in_stock_siblings, through: :design_group
  has_many :sibling_designs, through: :design_group
  has_many :images
  has_one  :master_img, -> { where(kind: 'master').limit(1) }, class_name: 'Image'
  has_many :addon_type_values
  has_many :variants, -> { where(show_variant: true).order('quantity = 0 ASC') }, inverse_of: :design
  has_many :wishlists, dependent: :destroy
  has_many :recent_items, dependent: :destroy
  has_one :master_image, -> { where(kind: 'master')}, class_name: 'Image'
  has_and_belongs_to_many :categories
  has_and_belongs_to_many :property_values
  has_many :reviews
  has_many :boosted_designs
  has_many :line_items
  has_many :sized_designs
  has_many :dynamic_prices
  has_many :dynamic_price_for_current_country, -> {where(country_code: Design.valid_dynamic_price_country_code).limit(1)} , :class_name => 'DynamicPrice'
  has_many :dynamic_price_for_domestic, -> {where(country_code: 'IN')} , :class_name => 'DynamicPrice'
  has_one :design_cluster
  has_many :design_addons
  has_many :addon_products, through: :design_addons
  has_many :in_stock_addon_products, through: :design_addons
  has_many :dynamic_size_charts
  has_many :designs_video_listing
  has_many :video_listings, through: :designs_video_listing
  has_many :design_grading_tags
  has_many :grading_tags, through: :design_grading_tags
  scope :for_ids_with_order, -> (ids) {
    where(:id => ids).order(sanitize_sql_array(
      ["position(id::text in ?)", ids.join(',')]))
  }
  has_many :follows, ->{where(followable_type: 'Design')}, :foreign_key => 'followable_id'
  has_many :design_promotion_pipe_lines

  scope :desc_graded, -> { order('designs.grade DESC, designs.id DESC') }
  scope :int_desc_graded, -> { order('designs.international_grade DESC, designs.id DESC') }
  scope :alphabetical, -> { order('property_values.name') }
  scope :h2l, -> { order('count(designs.id) desc') }
  scope :graded_only, -> { where.not(grade: nil, international_grade: nil) }
  scope :premium, -> { where(premium: true) }
  scope :rating_filter, -> { where('designs.average_rating > ?', DEFAULT_MIN_RATING.to_i) }
  scope :not_banned_in_catalog, lambda {|value|  where('in_catalog_one is null or in_catalog_one not in (?)', [0,3] << (value == :international ? 1 : 2))}
  scope :published, -> {where(state: 'in_stock', in_catalog_one: [nil, 1, 2]) }
  scope :in_stock, -> {where(state: 'in_stock') }
  friendly_id :title_and_category, use: [:slugged, :finders,  :history], slug_column: :cached_slug, sequence_separator: '--'
  searchable do
    dynamic_integer :graderank , separator: "_" do
      design_grading_tags.inject({}) do |hash, design_grading_tags|
        hash.merge( (design_grading_tags.grading_tag.name.to_s).to_sym => design_grading_tags.rank)
      end
    end
    text :category_name, boost: 5.0, more_like_this: true  do |design|
      name = ''
      design.categories.each do |category|
        name += category.name
      end
      name
    end
    text :designer_name, boost: 2.0, more_like_this: true do |design|
      design.designer.name if design.designer.present?
    end
    text :title, boost: 1.0, more_like_this: true
    text :tag_list, boost: 1.0, more_like_this: true do |design|
      design.tag_list.to_s
    end
    text :collection_list, more_like_this: true do |design|
      design.collection_list.to_s
    end
    text :catalogue_list, more_like_this: true do |design|
      design.catalogue_list.to_s
    end
    text :product_id do |design|
      design.id.to_s
    end

    text :keywords, as: :keywords
    text :imp_keywords, as: :imp_keywords

    integer :category_ids, multiple: true
    integer :category_parents, multiple: true
    text :description, more_like_this: true
    text :specification, more_like_this: true
    string :state
    integer :price
    integer :grade
    integer :grade_mobile
    integer :international_grade
    integer :international_grade_mobile
    integer :discount_price_bucket
    integer :created_at_bucket
    integer :sell_count_bucket
    integer :clicks_bucket
    integer :grade_bucket
    integer :grade_bucket_mobile
    integer :designer_id
    integer :discount_percent
    time :created_at, trie: true
    integer :discount_price
    integer :sell_count
    integer :in_catalog_one
    integer :cluster_winner do |design|
      design.is_winner? ? 1 : 0
    end
    integer :in_international_catalog
    integer :buy_get_free do |design|
      design[:buy_get_free]
    end
    float :graded_rating
    integer :average_rating do |design|
      design.average_rating.zero? && design.designer.present? ? design.designer.average_rating : design.average_rating
    end
    integer :designer_rating do |design|
      design.designer.average_rating.to_i
    end
    integer :review_count do |design|
      design.reviews.approved.with_enabled_designer_reviews.count
    end

    integer :property_value_ids, multiple: true do |design|
      design.property_value_ids
      # image cluster data is indexed from desktop
    end

    string(:cluster_id_str) { |d| d.design_cluster.present? ? d.design_cluster.cluster_id.to_s : "#{d.id}_0" }
    integer :design_cluster_score do |design|
      cluster = design.design_cluster
      cluster.present? ? cluster.score.to_i : 0
    end

    integer :option_type_value_ids, multiple: true do |design|
      design.get_option_type_values
    end

#app_souce: mobile => grading(dom_grade_mob, int_grade_mob)

    integer :dom_grade_mob do |design|
      design.dom_grade[DOM_GRADE_VARIABLE_MOBILE].to_i
    end
    integer :int_grade_mob do |design|
      design.int_grade[INT_GRADE_VARIABLE_MOBILE].to_i    
    end

#eid categories grading solr var
    integer :category_grade do |design|
      design.dom_grade[DOM_CATEGORY_GRADE_VARIABLE].to_i
    end

    integer :cod_shipper_ids, multiple: true do
      designer.cod_shipper_ids if designer.try(:cod)
    end

    text :collection_list, more_like_this: true do |design|
      design.collection_list.to_s
    end

    float :clicks_impression do |design|
      design.performance_metric_cibs.round(3)
    end

    float :trending_designs do |design|
      design.performance_metric_cibs(2).round(3)
    end

    float :odr_90_day do |design|
      #solr indexing will be done from desktop no need of any code code here.
    end

    integer :sell_count_30 do |design|
      design.metric_sales_30
    end

    boolean :premium
    boolean :featured_product
  end

  MIN_PRICE = MIN_PRICE_PER_PRODUCT.to_i
  MAX_PRICE = MAX_PRICE_PER_PRODUCT.to_i

  UNSTITCH_POPUP_ENABLE = ENV['UNSTITCH_POPUP_ENABLE'] == 'true'

  DESIGN_KEY_SPEC_SUB_GROUP = {
    'Saree' => {'saree' => ['color','fabric'].freeze,
                'blouse' => ['color','fabric'].freeze},
    'SalwarKameez' => {'kameez' => ['color','fabric'].freeze,
                       'dupatta' => ['color','fabric'].freeze,
                       'bottom' => ['color','fabric'].freeze}.freeze,
    'Kurti' => {'kameez' => ['color','fabric'].freeze}.freeze,
    'Lehenga' => {'kameez' => ['color','fabric'].freeze,
                  'dupatta' => ['color','fabric'].freeze,
                  'bottom' => ['color','fabric'].freeze}.freeze,
    'Jewellery' => {'other' => ['color','work'].freeze}.freeze,
    'HomeDecor' => {'other' => ['color','work','material'].freeze}.freeze,
    'Bag' => {'other' => ['color','material'].freeze}.freeze,
    'Jacket' => {'other' => ['color','work','material'].freeze}.freeze,
    'Turban' => {'other' => ['color','material'].freeze}.freeze
  }.freeze

  # States in which design is shown as sold out to customer
  UNPUBLISHED_STATES = ['reject', 'on_hold', 'seller_out_of_stock', 'delete',
    'banned', 'processing', 'review']

  STITCHING_ALLOWED_DESIGNABLE_TYPES      = ['SalwarKameez', 'Lehenga', 'Saree', 'Kurti'].freeze
  STITCHING_WITH_VARIANT_DESIGNABLE_TYPES = ['SalwarKameez', 'Kurti'].freeze
  HEIGHT_RANGE_FOR_STANDARD_ADDON = {
    anarkali: {default: 'on_the_ankle', 68 => 'on_the_calf', 74 => 'between_calf_and_knee'}.freeze,
    salwar_kameez: {default: 'on_the_knee', 73 => 'above_to_knee'}.freeze
  }.freeze

  #do not remove promise, because of request store
  if ENV['CLUSTER_CATEGORY_WISE_IDS'].to_s == '0'
    CLUSTER_CATEGORY_WISE_IDS = 0
  else
    CLUSTER_CATEGORY_WISE_IDS = promise do
      elligible_ids = []
      Category.where(id: ENV['CLUSTER_CATEGORY_WISE_IDS'].to_s.split(',')).each do |category|
        elligible_ids.push(*category.cached_self_and_descendants_id)
      end
      elligible_ids
    end
  end

  validates_length_of :title, within: 3..120
  validates_presence_of :images
  validates_presence_of :categories
  validates_inclusion_of :quantity, within: 0..99, on: :create
  validates_numericality_of :quantity
  validates_inclusion_of :discount_percent, within: 0..95
  validates_numericality_of :price, within: MIN_PRICE..MAX_PRICE
  validates_numericality_of :grade, only_integer: true, if: Proc.new {|design|
   design.grade.present? }
  validates :design_code, uniqueness: {scope: :designer_id}, on: :create
  validates :eta, numericality: { only_integer: true, allow_nil: true, greater_than_or_equal_to: 0, less_than_or_equal_to: 90 }

  before_save :update_triggers
  # after_save :update_design_state # State change is not needed in mobile app

  include RequestAccessor
  rattr_accessor :country_code

  include DesignPrice
  include UnbxdRecommendation

  # Attributes that require currency conversion
  # Needs to always be above Priceable
  PRICE_ATTR = [:discount_price, :price, :effective_price, :fd_effective_price]

  include Priceable

  handle_asynchronously :solr_index

  scope :in_stock, -> { where(state: 'in_stock', in_catalog_one: [1,2,nil]) }

  def self.find_by_cached_slug(slug)
    find slug
  rescue ActiveRecord::RecordNotFound => e
    nil
  end
  
  def custom_tags(country_code = Design.country_code)
    return country_code == 'IN' ? self.domestic_tag_list : self.international_tag_list
  end
  
  def valid_design?
    in_stock? && designer && master_image.present?
  end

  def self.valid_designs
    select(&:valid_design?)
  end

  # Performs necessary actions required before save
  #
  # == Returns:
  # Boolean
  #
  def update_triggers
    self[:graded_rating] = self[:total_review].to_i > 0 ? self[:average_rating].to_f ** (Math.log(self[:total_review].to_i, 6)) * self[:quality_level].to_f : 1.0
    # self.delay.flush_cache if self.created_at.present?
    self.discount_percent = 0 if self.discount_percent.blank?
    if self.discount_percent_changed?
      self.discount_price = ((100 - self.discount_percent) * self.price)/100
    end
    true
  end

  def unstitched_variant
    variants.find{|v| v.stitched == false}
  end

  def stitched_variants
    variants.select{|v| v.stitched == true}.sort_by{|v| v.option_type_values.present? ? v.option_type_values[0].position : 0} 
  end

  # Update state based on existing state and quantity
  #
  def update_design_state
    quantity_status = self.available?
    if UNPUBLISHED_STATES.exclude?(self.state) || (self.seller_out_of_stock? &&
      quantity_status)
      if quantity_status
        self.stock_added
      else
        # if self.pending_orders
        #   self.stock_blocked
        # else
          self.empty_stock
        # end
      end
    end
  end

  # Deletes cache
  #
  # == Returns:
  # Boolean
  #
  def flush_cache
    currencies = CurrencyConvert.currency_convert_memcached.each do |c|
      cache_list = cache_items(c.symbol)
      cache_list.each {|index, value| Rails.cache.delete(value)}
    end
    true
  end
  
  def warehouse_shipping_time(country = 'india', city = nil, user = nil)
    city_pdd, design_eta = 0, self.eta.to_i + self.designer.vacation_days_count.to_i
    lane_max_eta, for_all_vendor_pickup_locations = 0, false
    if country.to_s.downcase == 'india'
      pickup = 'bhiwandi'
      if city.present? && pickup.present?
        if LANE_FUNCTIONALITY
          lane_max_eta, for_all_vendor_pickup_locations = Lane.get_lane_eta(city.downcase, pickup)
        end
        if for_all_vendor_pickup_locations && lane_max_eta > 0
          city_pdd =  lane_max_eta
        else
          all_cities = [pickup, city.downcase].uniq
          city_pdd = (METRO_CITIES & all_cities).length == all_cities.length ? CITY_BASED_SHIP_TIME[:metro] : CITY_BASED_SHIP_TIME[:non_metro]
        end
      elsif pickup.present?
        if user.present?  && user.class == User && (drop = user.addresses.where(default: 1).last).present?
          lane_max_eta, for_all_vendor_pickup_locations = Lane.get_lane_eta(drop.try(:city).downcase, pickup)
          if for_all_vendor_pickup_locations && lane_max_eta > 0
            city_pdd =  lane_max_eta
          else
            all_cities = [pickup, drop.try(:city)].uniq
            city_pdd = (METRO_CITIES & all_cities).length == all_cities.length ? CITY_BASED_SHIP_TIME[:metro] : CITY_BASED_SHIP_TIME[:non_metro]
          end
        else
          city_pdd = Lane.get_max_range_eta_from_lane(pickup)
        end
      end
      promise_days = city_pdd.to_i + self.designer.ship_time + 2 #additional 2 days for QC and Dispatch
      promise_days += design_eta unless self.sor_available?
      promise_days += (DESIGNER_LSR.hours.to_f/1.days).round.to_i unless self.sor_available?
      promise_days += 1 if city_pdd.to_i > 0 && Time.current.advance(days: promise_days).sunday?
      return promise_days
    end
    promise_days = design_eta + self.designer.ship_time + self.categories.collect(&:eta).max.to_i
    promise_days += (DESIGNER_LSR.hours.to_f/1.days).round.to_i
    promise_days += 1 if city_pdd.to_i > 0 && Time.current.advance(days: promise_days).sunday?
    return promise_days
  end

  def currently_boosted?(category_id)
    self.boosted_designs.active.where({category_id: category_id}).exists?
  end

  def designer_shipping_time(country = 'india', city = nil, user = nil)
    city_pdd, design_eta = 0, self.eta.to_i + self.designer.vacation_days_count.to_i
    lane_max_eta, for_all_vendor_pickup_locations = 0, false
    if country.to_s.downcase == 'india'
      pickup = self.sor_available? ? 'bhiwandi' : designer.pickup_location.try(:downcase)
      if city.present? && pickup.present?
        if LANE_FUNCTIONALITY
          lane_max_eta, for_all_vendor_pickup_locations = Lane.get_lane_eta(city.downcase, pickup)
        end
        if for_all_vendor_pickup_locations && lane_max_eta > 0
          city_pdd =  lane_max_eta
        else
          all_cities = [pickup, city.downcase].uniq
          city_pdd = (METRO_CITIES & all_cities).length == all_cities.length ? CITY_BASED_SHIP_TIME[:metro] : CITY_BASED_SHIP_TIME[:non_metro]
        end
      elsif pickup.present?
        if user.present?  && user.class == User && (drop = user.addresses.where(default: 1).last).present?
          lane_max_eta, for_all_vendor_pickup_locations = Lane.get_lane_eta(drop.try(:city).downcase, pickup)
          if for_all_vendor_pickup_locations && lane_max_eta > 0
            city_pdd =  lane_max_eta
          else
            all_cities = [pickup, drop.try(:city)].uniq
            city_pdd = (METRO_CITIES & all_cities).length == all_cities.length ? CITY_BASED_SHIP_TIME[:metro] : CITY_BASED_SHIP_TIME[:non_metro]
          end
        else
          city_pdd = Lane.get_max_range_eta_from_lane(pickup)
        end
      end
      promise_days = city_pdd.to_i
      promise_days += design_eta unless self.sor_available?
      promise_days += (DESIGNER_LSR.hours.to_f/1.days).round.to_i unless self.sor_available?
      promise_days += 1 if city_pdd > 0 && Time.current.advance(days: promise_days).sunday?
      return promise_days
    end
    promise_days = design_eta + self.designer.ship_time + self.categories.collect(&:eta).max.to_i
    promise_days += (DESIGNER_LSR.hours.to_f/1.days).round.to_i
    promise_days += 1 if city_pdd > 0 && Time.current.advance(days: promise_days).sunday?
    return promise_days
  end

  def not_rts_shipping_time(country = 'india')
    (Country.country_wise_delivery_time(country) + self.designer_shipping_time(country)).round
  end

  def size_in_warehouse?(bucket = nil, quantity: 1)
    self.sized_designs.find{|sd| sd.size_bucket_id == (bucket || SizeChart::UNSTITCHED_SIZE_CHART.try(:size_bucket_id))}.try(:quantity).to_i >= quantity
  end

  def delivery_date_for_rts(country = 'india')
    Country.country_wise_delivery_time(country.try(:downcase))
  end

  # List of cache keys for design for symbol
  #
  # == Parameters:
  # symbol::
  #   String
  #
  # == Returns:
  # Hash
  #
  def cache_items(symbol)
    cid = "design_#{self.id}:#{symbol}_"
    blocks = 'details', 'img', 'wholesale', 'seo_og', 'microdata_header',
     'similar'
    cache_list = {}
    blocks.each {|b| cache_list[b] = "#{cid}b" }
    cache_list
  end

  def approx_weight(country_code = Design.country_code)
    if country_code.present? && (country_hash = CurrencyConvert.currency_convert_cache_by_country_code(country_code)).present?
      on_design_rate = Promotion.get_free_shipping(country_hash.country, true)
      return 0 if on_design_rate.last.present? && on_design_rate.first > 0 && self.effective_price_currency(country_hash.rate).round(0) >= (on_design_rate.first/country_hash.rate) && ((self.categories.pluck(:id) & EXCLUDE_FREE_SHIPPING_CATEGORIES).blank? && ![1,2,3].include?(self.buy_get_free))
    end
    categories.collect(&:weight).compact.max || 0
  end

  def free_shipping_eligible?
    cc = CurrencyConvert.currency_convert_cache_by_country_code(Design.country_code)
    country = cc.country
    conversion_rate = cc.rate
    on_design_price = Promotion.get_free_shipping(country, true)
    if on_design_price.present? && on_design_price.first > 0 && (self.effective_price / conversion_rate).round(2) >= on_design_price.first
      return true
    end
  end

  def free_shipping_eligibility(discount_price)
    currency_convert = CurrencyConvert.currency_convert_memcached.find{|c| c.country_code == Design.country_code}
    return domestic_shipping_eligibility?(discount_price) if Design.country_code == 'IN'
    on_design_price = Promotion.get_free_shipping(currency_convert.country, true)
    return on_design_price.present? && on_design_price.first > 0 && discount_price.round(0) >= (on_design_price.first/currency_convert.rate) && !([1,2,3].include?(buy_get_free) && (bmgnx_hash = PromotionPipeLine.bmgnx_hash).present?)
  end

  def domestic_shipping_eligibility?(discount_price)
    DOMESTIC_SHIPPING_CHARGES.keys.first.to_i < discount_price
  end

  def weight_in_imperial
    if designable_type == 'Consumable'
      if weight.to_i >= 454
        weight_in_lb = (weight.to_i * 0.00220462).to_d.truncate(2).to_f
        "#{weight} gms | #{weight_in_lb} lb"
      else
        weight_in_oz = (weight.to_i * 0.035274).to_d.truncate(2).to_f
        "#{weight} gms | #{weight_in_oz} oz"
      end
    else
      nil
    end
  end

  def is_winner?
    if design_cluster.present? && (winner = design_cluster.in_stock_winner).present? && self.id != winner.design_id
      return false
    end
    true
  end


  # Checks whether stock is available
  #
  # == Returns:
  # Boolean
  #
  def available?
    unless ['banned', 'on_hold', 'inactive'].include?(self.designer.state_machine)
      self.variants.present? ? self.variants_available? :
        (self.in_stock? && self.quantity.to_i > 0)
    end
  end

  # Checks whether variant stock is available
  #
  # == Returns:
  # Boolean
  #
  def variants_available?
    if variants.loaded?
      variants.any?{|v| v.quantity.to_i > 0}
    else
      variants.stock_available.exists?
    end
  end

  # Update the design average review and total review
  #
  def update_avg_ratings
    enabled_reviews = self.reviews.approved.with_enabled_designer_reviews
    self.update_column('total_review', enabled_reviews.count)
    self.update_column('average_rating', enabled_reviews.average('rating').round(1))
    self.update_column('average_rating_domestic', enabled_reviews.where(:geo => 'domestic').average('rating').round(1))
    self.update_column('average_rating_international', enabled_reviews.where(:geo => 'international').average('rating').round(1))
  end

  # State Machine
  # In Stock - Quantity > 0
  # Blocked - stock blocked in pending orders
  # On Hold - Designer Vacation
  state_machine initial: :sold_out do
    before_transition to: :in_stock do |design|
      design.last_in_stock = Time.now
      if design.designer.vacation?
        design.designer_vacation
        false
      end
    end

    before_transition to: :sold_out do |design|
      design.last_sold_out = Time.now
    end

    event :stock_blocked do
      transition in_stock: :blocked
    end

    before_transition to: :blocked do |design|
      design.last_blocked = Time.now
    end

    event :designer_vacation do
      transition all => :on_hold
    end

    event :stock_added do
      transition [:processing, :sold_out, :seller_out_of_stock, :blocked,
        :banned, :review, :on_hold, :reject] => :in_stock
    end

    event :stock_removed do
      transition [:blocked, :in_stock, :sold_out] => :seller_out_of_stock
    end
  end

  # Get addons and all its association based on params
  #
  # == Parameters:
  # country_code::
  #   String
  # currency::
  #   String
  #
  # == Returns:
  # ActiveRecord::Relation
  #

  def vendor_addon_category?
    @vendor_addon_category ||= (self.category_parents_name.to_a & VENDOR_ADDON_CATEGORY).present?
  end

  def addons_association_not_for(region)
    category_id = nil

    ## Below condition is added to allow Standard Stiching for SOR and Classiques Lehega Product

    product_in_warehouse = self.sor_available? || self.designer_id == 12727

    if DESIGNER_ADDON && (vendor_addon_category? || (region != 'domestic' && !(product_in_warehouse && ( self.designable_type == 'Lehenga' || self.designable_type == 'Saree' || self.designable_type == 'SalwarKameez') ) ) )
      
      #self.category_parents_name.include?('rakhi-online')
      categories_ids = self.categories.map{|i| i.cached_self_and_ancestors_id}.flatten.uniq
      o_clause = 'addon_types.position, addon_type_values.position, addon_option_types.position, addon_option_values.position'
      addon_types = AddonType.includes(addon_type_values: [addon_option_values: :addon_option_type]).order(o_clause).
      where(addon_type_values: {published: true, designer_id: self.designer_id}, addon_types: {category_id: categories_ids}).
      where('addon_type_values.quantity <> 0').uniq
    elsif variants.present? && (Design::STITCHING_ALLOWED_DESIGNABLE_TYPES.exclude?(self.designable_type) || unstitched_variant.blank?)
      []
    else
      if self.designable_type == 'Saree'
        if self.category_parents_name.include?('Sarees' || 'gifts' || 'kids') 
          category_id = Category.find_by_namei('sarees').id
        else 
          category_id = Category.find_by_namei('blouse').id
        end
      elsif self.designable_type == 'SalwarKameez'
        category_id = Category.find_by_namei('salwar-kameez').id
      elsif self.designable_type == 'Lehenga'
        category_id = Category.find_by_namei('lehengas').id
      elsif self.designable_type == 'Kurti'
        category_id = Category.find_by_namei('kurtas-and-kurtis').id
      end
      RequestStore.cache_fetch("mobile/design_addons_not_for/#{category_id}/#{region}", expires_in: 24.hours) do
        addon_types = AddonType.includes(addon_type_values:
          [:addon_type_value_group, addon_option_values: :addon_option_type]).
          where(category_id: category_id).
          where(addon_type_values: {published: true, design_id: nil, designer_id: nil}).
          where{addon_type.addon_type_values.visible_for != region}.
          order('addon_types.position, addon_type_values.position, addon_option_types.position, addon_option_values.position').uniq.to_a
      end
    end
  end

  # Get title and category compination for friendly id search
  #
  # == Returns:
  # String
  #
  
  def category_parents_name
    parents = Array.new
    self.categories.each {|cat| parents += cat.cached_self_and_ancestors_name }
    parents.uniq
  end

  def title_and_category
    category_name = self.categories.first.try(:name).try(:singularize)
    if self.title.match(/#{category_name}/i).nil? && category_name.downcase != "other"
      "#{self.title} #{category_name.downcase}"
    else
      self.title.downcase
    end
  end

  # Check whether international shiping is available
  #
  # == Returns:
  # Boolean
  #
  def can_ship_international?
    !self.categories.where(international: 'N').exists?
  end

  # Checks whether design is stitchable
  #
  # == Returns:
  # Boolean
  #
  def stitchable?(country_code)
    STITCHING_ENABLE == 'true' && country_code != 'IN' && Design::STITCHING_ALLOWED_DESIGNABLE_TYPES.include?(self.designable_type)
  end

  def self.similar_designs(current_design)
    RequestStore.cache_fetch("similar_designs_#{current_design.id}" , expires_in: 24.hour) do
      begin
        Sunspot.more_like_this(current_design) do
          fields :category_name, :designer_name, :title, :description, :specification
          with(:state, 'in_stock')
          with(:average_rating).greater_than_or_equal_to(MOBILE_RATING_THRESHOLD)
          without(:in_catalog_one, 0)
          without(:in_catalog_one, 3)
          with(:cluster_winner, 1)
          if Design.country_code !='IN'
            without(:in_catalog_one,1)
          else
            without(:in_catalog_one,2)
          end
          Sunspot.config.pagination.default_per_page = 16
        end.results
      rescue => e
        Design.in_stock.where(designable_type: 'Jewellery').order('created_at DESC').limit(16)
      end
    end
  end

  def self.get_recommended_designs_unbxd(user_id, country_code)
    query = { uid: user_id, format: 'json' }
    response = JSON.parse(UnbxdRecommendation.get_recommended_designs({query: query}).body) rescue nil
    if response.present? && response['Recommendations'].present?
      design_ids = []
      catalog = (country_code.present? && country_code != 'IN') ? :international : :domestic
      response['Recommendations'].each{ |recommendation| design_ids << recommendation['uniqueId']}
      Design.in_stock.not_banned_in_catalog(catalog).where(id: design_ids)
    end
  end

  def self.similar_designs_unbxd(current_design, user_id, country_code = nil)
    # query = { uid: user_id, format: 'json' }
    # response = JSON.parse(UnbxdRecommendation.more_like_this(current_design.id, {query: query}).body) rescue nil
    response = nil
    if response.present? && response['status'] == 200 && response['Recommendations'].present?
      design_ids = []
      if country_code.present?
        catalog = country_code == 'IN' ? :domestic : :international
      else
        catalog = :domestic
      end
      response['Recommendations'].each{ |recommendation| design_ids << recommendation['uniqueId']}
      Design.in_stock.not_banned_in_catalog(catalog).where(id: design_ids)
    elsif current_design.present?
      Design.similar_designs(current_design)
    else
      []
    end
  end

  def get_addon_img_and_msg_to_show_for_height
    stitching_height_value, stitching_height_message, stitching_height_images = [], [], []
    categories_ids = self.categories.collect(&:id)
    is_anarkali = (ANARKALI_CATEGORY_FOR_STANDARD_ADDON & categories_ids ).present? ? :anarkali : :salwar_kameez
    height_range = Design::HEIGHT_RANGE_FOR_STANDARD_ADDON[is_anarkali]
    # IMP: Below in image_selecting_range replace :default with starting position so to handle easily at client end
    height_range.each do |height,type| 
      stitching_height_value << (height == :default ? 48.to_i : height.to_i)
      stitching_height_message << "For the selected size and your full body height kameez will fall #{type} area".tr("_"," ")
      stitching_height_images << ActionController::Base.helpers.image_url("#{type}.jpg")
    end
    return stitching_height_value, stitching_height_message, stitching_height_images
  end

  def wishlist_for_user(id)
    wishlists.unordered.wished.find_by_user_id(id)
  end

  def self.valid_dynamic_price_country_code
    SWITCH_DP_TO_INTERNATIONAL ? 'US' : (Design.country_code || 'US')
  end

  def self.inr_value(price, country_code, rate)
    if (market_rate = CurrencyConvert.countries_marketrate[country_code]).present?
      ((price/rate)*market_rate).round(CurrencyConvert.round_to)
    else
      price
    end
  end

  def rts_available(country)
    (self.sor_available? || self.variants.any?{|v| v.sor_available?})
  end

  def self.rts_tnc
    @rts_tnc_messages ||=
      [
        "Frequently Asked Questions:",
        "Q: What is ready to ship? \n A: The products marked as Ready To Ship (RTS) are the products which are present in Mirraw Warehouse and are available to be shipped on the same day as the order is placed.",
        "Q: How is it helpful? \n A: Ready to Ship is helpful in cases when you need the product to be delivered much earlier than the usual delivery time.",
        "Q: Does Mirraw apply any charges for Ready to ship products? \n A: No, the RTS products are not levied with any extra charges.",
        "Q: Does the Estimated delivery of Ready To Ship products vary in any condition? \n A: Yes, it can vary in the following two conditions: \n 1. If you add stitching to your product. You will be able to see the new date there itself as the product is available at the Mirraw warehouse but needs to be stitched before it can get dispatched. \n 2. If you add Ready to ship and Non-ready to ship products to your cart. In that case, we will consider the maximum estimated delivery date which will be visible to you.",
        "Q: What if I want to shop both Ready To Ship and Non - Ready To Ship products? \n A: We’d advise you to create separate orders for both Ready to Ship and Non-Ready to ship products as we deliver the whole order together. This would ensure that you get the RTS products on time as committed.",
        "Terms and Conditions:",
        "Please check the estimated date before you buy the Ready To Ship Product.",
        "The Estimated Date changes based on the country you want the product to be shipped.",
        "The Estimated Delivery Date changes if you add stitching to your orders. Please make sure that you check the Estimated Delivery Date if you add stitching to make sure if the updated date satisfies your need for the product.",
        "Adding Multiple products to the cart which includes both Ready To Ship and Non - Ready To Ship Products may change the Expected Delivery Date. Hence, in this case, we suggest you place two different orders for both Ready To Ship and Non - Ready To Ship products to avoid delay in delivery."
      ]
  end

  def buy_get_free
    (bmgnx_hash = PromotionPipeLine.bmgnx_hash).present? && (bmgnx_hash[:buy_get_free].to_i == 1) && (discount_price < bmgnx_hash[:filter].to_i) ? bmgnx_hash[:buy_get_free] : self[:buy_get_free]
  end

  def self.update_effective_prices(symbol, response, country_code)
    unique_ids = response['Recommendations'].collect{ |design| design['uniqueId']}
    preload_array = [:dynamic_price_for_current_country,:designer,:master_image]
    active_promotions = PromotionPipeLine.active_promotions
    preload_array << :categories unless Promotion.discount_offer_on_category(active_promotions).blank?
    catalog = country_code == 'IN' ? :domestic : :international
    designs = Design.includes(preload_array).not_banned_in_catalog(catalog).where(id: unique_ids).where(state: 'in_stock')
    design_hash = Hash.new
    designs.each do |design|
      design_hash[design.id.to_s] = [design.effective_price_currency(1),
        design.try(:master_image).try(:photo, :long), design.average_rating,
        design.designer.to_param, design.to_param, design.categories.first.try(:name)]
    end.flatten
    response['Recommendations'].delete_if{|design| design_hash.keys.exclude?(design['uniqueId'])}
    response['Recommendations'].each do |design|
      design['discount_price'] = CurrencyConvert.to_currency(country_code, design_hash[design['uniqueId']][0] || design['discount_price'].to_i).round(2)
      design['imageUrl'] = design_hash[design['uniqueId']][1]
      design['rating'] = design_hash[design['uniqueId']][2]
      design['designer_slug'] = design_hash[design['uniqueId']][3]
      design['design_slug'] = design_hash[design['uniqueId']][4]
      design['category_name'] = design_hash[design['uniqueId']][5]
    end
    response
  end

  def get_shipping_info(rate, country, cart, symbol)
    offers = cart.conditional_offers(rate, country) if cart.present?
    if offers.present? && offers[:shipping].present?
      amount = ApplicationController.helpers.get_price_with_symbol(offers[:shipping][:free_shipping_at], symbol)
      if (category = offers[:shipping][:on_category]).present? && self.categories.pluck(:name).include?(category)
        "Free shipping limited time offer on purchase of #{category.capitalize.pluralize} above #{amount}"
      else
        "Free shipping limited time offer on purchase of products above #{amount}"
      end
    elsif (country.present? && country.downcase != 'india') || (Design.country_code != 'IN')
      "Shipping charge of approximately #{symbol} #{categories.first.approximate_shipping(country, rate).floor} may be applicable."
    end
  end

  def applicable_promotions_end_date(offer, country_code)
    dates = []
    if flash_deals_available? && (flash_deal = design_promotion_pipe_lines.active_flash_deals.first).present?
      return flash_deal.end_date.to_datetime.strftime('%Q')
    elsif offer.present?
      if offer.name.include?('category_discount_sale')
        category = JSON.parse(offer.variables_hash)['sale_on']
        if self.category_parents_name.any? {|a| category.include? a.downcase}
          dates << offer.end_date.to_datetime
        end
      elsif offer.name == 'b1g1'
        dates << offer.end_date.to_datetime if buy_get_free == 1
      else
        dates << offer.end_date.to_datetime
      end
    end
    dates << (self.designer.vendor_additional_discount_percent > 0 ?
     self.designer.additional_discount_end_date.end_of_day.to_datetime : nil)
    (date = dates.compact.min).present? && date <= 120.hours.from_now ? date.strftime('%Q') : nil
  end

  def promotion_messages(country_code, rate, symbol, app_source=nil, app_version=nil)
    offers = []
    # if Promotion.stitching_offer_on? && self.stitchable?(country_code)
    #   offers << "Stitching cost #{ApplicationController.helpers.get_price_with_symbol(ApplicationController.helpers.get_price_in_currency(Promotion.stitching_offer[0],rate), symbol)} for products above #{ApplicationController.helpers.get_price_with_symbol(ApplicationController.helpers.get_price_in_currency(Promotion.stitching_offer[1],rate),symbol)}"
    # end
    if buy_get_free == 1 && (bmgnx_hash = PromotionPipeLine.bmgnx_hash).present?
      offers << Promotion.bmgnx_offer_message(bmgnx_hash)
    elsif (qpm_message = QuantityDiscountPromotion.design_message).present?
      offers << qpm_message
    end
    if app_source.present? && (app_source.downcase == 'mobile' || app_version.present?)
      if (prepaid_message = PrepaidPaymentPromotion.design_message(country_code)).present? && (app_source.downcase == 'mobile' || app_version > PREPAID_PROMOTION_MINIMUM_VERSION[app_source])
        offers << prepaid_message
      end
      if (shipping_message = PrepaidShippingPromotion.design_message(country_code)).present? && (app_source.downcase == 'mobile' || app_version > PREPAID_PROMOTION_MINIMUM_VERSION[app_source])
        offers << shipping_message
      end
    end
    offers
  end

  def product_current_offers(country_code, country_name=nil, app_source=nil, app_version=nil)
    current_offers = []
    if buy_get_free == 1 && (bmgnx_hash = PromotionPipeLine.bmgnx_hash).present?
      current_offers << {title: Promotion.bmgnx_offer_message(bmgnx_hash), tnc: Promotion.bmgnx_tnc(bmgnx_hash)}
    end
    if country_code != 'IN' && (percentage = Wallet.cashback_percent.to_f) > 0.0
      current_offers << {title: "#{percentage}% Cashback offer", tnc: Wallet.cashback_tnc}
    end
    if country_name && self.rts_available(country_name)
      current_offers << {title: 'Ready To Ship', tnc: Design.rts_tnc}
    end
    if (qpm_list = QuantityDiscountPromotion.current_active_promotions).present?
      qpm_list.each_with_index do |qpm, index|
        current_offers << {title: qpm.design_message, tnc: (index == 0 ? QuantityDiscountPromotion.tnc_message : nil)}
      end
    end
    if self.approx_weight(country_code) == 0
      current_offers << {title: 'Free Shipping'}
    end
    if app_source.present? && (app_source.downcase == 'mobile' || app_version.present?)
      if country_code == 'IN' && (prepaid_message = PrepaidPaymentPromotion.design_message(country_code)).present? && (app_source.downcase == 'mobile' || app_version > PREPAID_PROMOTION_MINIMUM_VERSION[app_source])
        current_offers << {title: prepaid_message, tnc: PrepaidPaymentPromotion.tnc_message}
      end
      if country_code == 'IN' && DOMESTIC_PREPAID_SHIPPING_PROMOTION && (shipping_message = PrepaidShippingPromotion.design_message(country_code)).present? && (app_source.downcase == 'mobile' || app_version > PREPAID_PROMOTION_MINIMUM_VERSION[app_source])
        current_offers << {title: shipping_message, tnc: PrepaidShippingPromotion.tnc_message}
      end
    end
    current_offers
  end

  def product_offer(country_code, description_msg=nil, with_qpm: false)
    offer = {}
    if flash_deals_available?
      offer.merge!(type: 'flash_deals', msg: 'Flash Sale', key: 'flash_deals', value: '1', tnc: nil, description_msg: nil)
    elsif buy_get_free == 1 && (bmgnx_hash = PromotionPipeLine.bmgnx_hash).present?
      unless description_msg
        description_msg = "Buy #{bmgnx_hash[:m]} #{'item'.pluralize(bmgnx_hash[:m])} and get "
        description_msg += bmgnx_hash[:x] != 100 ? "#{bmgnx_hash[:x]}% off on next #{bmgnx_hash[:n]} #{'item'.pluralize(bmgnx_hash[:n])}" : "next #{bmgnx_hash[:n]} #{'item'.pluralize(bmgnx_hash[:n])} free" 
      end
      offer.merge!(type: 'bmgnx', msg: Promotion.bmgnx_offer_message(bmgnx_hash), key: 'buy_get_free', value: '1', tnc: Promotion.bmgnx_tnc(bmgnx_hash), description_msg: description_msg)
    elsif with_qpm && (qpm_message = QuantityDiscountPromotion.design_message).present?
      offer.merge!(type: 'qpm', msg: qpm_message, tnc: QuantityDiscountPromotion.tnc_message)
    end
    offer
  end

  def self.plus_size_enable?(request)
    if ENV['PLUS_SIZE_SHOW'] == 'true'
      User.app_source.to_s == 'mobile' ? true : ['android','ios'].include?(request.headers['App-Source'].to_s.downcase.split('-')[0]) && request.headers['App-Version'].to_s > PLUS_SIZE['PLUS_SIZE_MINIMUM_VERSION'][request.headers['App-Source'].to_s.downcase.split('-')[0]].to_s
    end
  end

  def self.get_feed_designs(app_source, app_version)
    app_source = 'staged_app' if SystemConstant.get('STAGED_APP') == "#{app_source}-#{app_version}"
    app_source = app_source.split('-')[-1].downcase
    case app_source
    when 'staged_app'
      Rails.cache.fetch('staged_app_feed', expires_in: API_CACHE_LIFESPAN.minutes) do
        Design.in_stock.graded_only.rating_filter.limit(20).includes(:designer, :images).to_a
      end
    when 'android'
      Rails.cache.fetch('home_page_feed', expires_in: API_CACHE_LIFESPAN.minutes) do
        Design.in_stock.graded_only.rating_filter.limit(20).includes(:designer, :images).to_a
      end
    when 'premium_main'
      Rails.cache.fetch('home_page_feed_premium', expires_in: API_CACHE_LIFESPAN.minutes) do
        Design.in_stock.graded_only.rating_filter.premium.limit(20).includes(:designer, :images).to_a
      end
    else
      send("feed_for_#{app_source}")
    end
  rescue
    Rails.cache.fetch('home_page_feed', expires_in: API_CACHE_LIFESPAN.minutes) do
      Design.in_stock.graded_only.rating_filter.limit(20).includes(:designer, :images).to_a
    end
  end

  ANDROID_SUB_APPS.keys.each do |method_name|
    define_singleton_method("feed_for_#{method_name}".to_sym) do
      Rails.cache.fetch("home_page_feed_#{method_name}", expires_in: API_CACHE_LIFESPAN.minutes) do
        method_name.include?('premium') ? premium_sub_app_designs(method_name.to_s) : sub_app_designs(method_name.to_s)
      end
    end
  end

  def self.sub_app_designs(app)
    if (designs = Category.find_by_namei(ANDROID_SUB_APPS[app]['sub']).designs.in_stock.graded_only.rating_filter.limit(20).includes(:designer, :images).to_a).present?
      designs
    else
      Rails.cache.fetch('home_page_feed', expires_in: API_CACHE_LIFESPAN.minutes) do
        Design.in_stock.graded_only.rating_filter.limit(20).includes(:designer, :images).to_a
      end
    end
  end

  def self.premium_sub_app_designs(app)
    if (designs = Category.find_by_namei(ANDROID_SUB_APPS[app]['sub']).designs.in_stock.graded_only.rating_filter.premium.limit(20).includes(:designer, :images).to_a).present?
      designs
    else
      Rails.cache.fetch('home_page_feed_premium', expires_in: API_CACHE_LIFESPAN.minutes) do
        Design.in_stock.graded_only.rating_filter.premium.limit(20).includes(:designer, :images).to_a
      end
    end
  end

  def increment_count(attr)
    update_column(attr, (send(attr).to_i + 1))
  end

  def decrement_count(attr)
    update_column(attr, (send(attr).to_i - 1))
  end

  def self.get_message_for(design_quantity,number_of_line_items)
    if number_of_line_items > 0
      span_style = "color='#F44336' style='color:#F44336;'"
      if design_quantity <= 2 and number_of_line_items > 2
        return "This product will most likely be <b><font #{span_style}>Sold Out</font></b> in a few minutes."
      elsif number_of_line_items > 5
        return "Fast Mover: <b><font #{span_style}>#{number_of_line_items} people </font></b>added this to cart today! "
      elsif number_of_line_items == 1
        return "<b><font #{span_style}>#{number_of_line_items} person </font></b>added this to cart today!"
      else
        return "<b><font #{span_style}>#{number_of_line_items} people </font></b>added this to cart today!"
      end
    end
    ''
  end

  #delayed
  def self.individual_less_qty_notification_android_wishlist(account_id, design_id)
  end

  def self.individual_less_qty_notification_android_cart(account_id, design_id, app_source)
  end

  def dynamic_size_chart_hash
    chart = dynamic_size_charts.order('updated_at desc').first.presence.try(:to_h)
    return chart if chart
    if variants.present?
      dynamic_size_chart_category.try do |category|
        category.dynamic_size_charts.find_by_designer_id(designer_id) || category.default_size_chart
      end.try(:to_h)
    end
  end

  def review_opinion
    # return unless total_review.to_i > 3
    # if average_rating >= 3.5
    #   average_rating > 4.5 ? 'Amazing reviews, Great Product!' : 'Satisfied customers, Good Product.'
    # elsif average_rating < 3.5
    #   average_rating > 2 ? 'Mixed reviews on this product.': 'Product has been rated low. Read customer reviews.'
    # else
    #   'Mixed ratings on this product.'
    # end
  end

  def self.app_specific_grading?
    Rails.cache.fetch('use_mobile_grade', expires_in: API_CACHE_LIFESPAN.minutes) do
      SystemConstant.get('USE_MOBILE_GRADE') == 'true'
    end
  end

  def grade
    Design.app_specific_grading? ? self[:grade_mobile] : self[:grade]
  end

  def international_grade
    Design.app_specific_grading? ? self[:international_grade_mobile] : self[:international_grade]
  end
  
  def stitchable_quantity
    package_details.to_s.scan(/\d+/).collect(&:to_i).max.try(:nonzero?) || 1
  end

  def experiment_id(experiments)
    product_type = designable_type.to_s.downcase.gsub(/[-_]/,'')
    if experiments.is_a?(Hash) && product_type.present?
      experiments.keys.each do |category|
        return experiments[category] if product_type.include?(category) || category == 'all'
      end
    end
    product_type.present? ? "default_#{product_type}" : 'other'
  end

  def performance_metric_cibs(metric=1)
    case metric
    when 1
      if sell_count.to_i > 0 && clicks.to_i > 0 && clicks.to_i < 300
        ((sell_count * 100 / clicks.to_f) * discount_price)
      else
        0.0
      end
    when 2
      if sell_count.to_i > 0 && clicks.to_i > 0 && clicks.to_i < 300
        (((sell_count ** 1.2) * 100 / clicks.to_f) * discount_price)
      else
        0.0
      end
    else
      0.0
    end
  end

  def mirraw_recommended?
    average_rating.to_f >= 3.5
  end

  def cart_addon
    Rails.cache.fetch("#{designable_type}_design_cart_addons",expires_in: 24.hours) do
      Design.where(id: CART_ADDON[designable_type]).to_a
    end
  end

  def cart_addon?
    CART_ADDON.to_h.values.map(&:values).flatten.include?(id)
  end

  def stitching_testimonials(country_code)
    if country_code != 'IN'
      STITCHING_TESTIMONIALS[designable_type].to_a.sample(4)
    end
  end

  def eta
    super.to_i <= 0 ? designer.eta : super.to_f
  end

  def ga_data(other_data={})
    {
      'id' => id.to_s, 'name' => "#{id}_", 'category' => categories.first.name,
      'brand' => designer.cached_slug, 'price' => effective_price_currency(1)
    }.merge(other_data.compact)
  end
  
  def ga_data_v2(other_data={})
    country_code = Design.country_code
    conversion_rate = CurrencyConvert.currency_convert_cache_by_country_code(Design.country_code).rate
    market_rate = CurrencyConvert.countries_marketrate[country_code] || 1
    inr_discount_price = (effective_price_currency(conversion_rate) * market_rate).round(CurrencyConvert.round_to)
    main_price = (price_currency(conversion_rate) * market_rate).round(CurrencyConvert.round_to)
    hash = categories.first.breadcrumb_path
    crumbs_category = hash.keys
    array_index = crumbs_category.length > 2 ? 2 : 1
    item_title = crumbs_category[array_index]
    item_listing_name, item_listing_id = [item_title, Category.find_by("LOWER(title) = '#{item_title.try(:downcase)}'").try(:id)] if item_title.present?
    country_code = Design.country_code
    discount = (main_price - inr_discount_price).round(CurrencyConvert.round_to)
    url = Rails.application.routes.url_helpers.designer_design_path(self.designer, self)
    {
      'item_name' => title,
      'item_id' => id.to_s,
      'item_category' => crumbs_category[1] || "",
      'item_category2' => crumbs_category[2] || "",
      'item_category3' => crumbs_category[3] || "",
      'item_category4' => crumbs_category[4] || "",
      'item_category5' => crumbs_category[5] || "",
      'affiliation' => "",
      'item_brand' => designer.cached_slug,
      'discount' => discount || 0,
      'price' => inr_discount_price,
      'location_id' => "",
      'item_variant' => color || "",
      'item_list_id' => item_listing_id.to_s || "",
      'item_list_name' => item_listing_name || "",
      'coupon' => "",
      'quantity' => 1,
      'content_category' => "#{self.categories.first.name}".titleize,
      'image_url' => "https://#{self.master_img.try(:photo, :zoom)}",
      'image_thumbnail_url' => "https://#{self.master_img.try(:photo, :thumb)}",
      'item_created' => self.created_at.strftime('%Y-%m-%d'),
      'item_url' => "https://www.mirraw.com#{url}"
    }.merge(other_data.compact)
  end
  

  def check_if_plus_size_serviceable(size)
    return size.to_i <= designable.max_bustsize_fit.to_i if system_plus_size
    (check_if_design_allowed_for_plus_size || (size.to_i <= ENABLE_PLUS_SIZE['max_size']))
  end

  def check_if_design_allowed_for_plus_size
    @plus_size_check ||= (ENABLE_PLUS_SIZE['enable'] || ENABLE_PLUS_SIZE['exceptions'].include?(id))
  end

  def system_plus_size
    @plus_size ||= (designable.respond_to?(:max_bustsize_fit) && (((ENABLE_PLUS_SIZE['max_size'].to_i.nonzero? || 42)+1)..54).include?(designable.max_bustsize_fit.to_i))
  end

  def get_max_bust_size
    if self.designable.respond_to?(:max_bustsize_fit)
      max_size = self.designable.max_bustsize_fit.to_i
      return max_size.odd? ? (max_size - 1) : max_size
    end
    0
  end

  def self.is_luxury_category?(design_id)
    d = Design.where(id: design_id).preload(:categories).first
    if d.present?
      return true  if d.designer.designer_type == "Tier 1 Designer"
      luxe = Category.where(name: "luxe").first
      if luxe.present?
        child_category = luxe.self_and_descendants.pluck(:name)
        return true if (child_category & d.categories.pluck(:name)).present?
      end
    end
    return false
  end

  def self.get_city_based_pdd(delivery_city, vendor_pickup_locations)
    if LANE_FUNCTIONALITY && vendor_pickup_locations.present?
      lane_max_eta, for_all_vendor_pickup_locations = Lane.get_lane_eta(delivery_city.downcase, vendor_pickup_locations)
    end
    if for_all_vendor_pickup_locations && lane_max_eta > 0
      return lane_max_eta
    else
      all_cities = (vendor_pickup_locations + [delivery_city.downcase]).uniq.compact
      return (METRO_CITIES & all_cities).length == all_cities.length ? CITY_BASED_SHIP_TIME[:metro] : CITY_BASED_SHIP_TIME[:non_metro]
    end
  end

  def get_sibling_designs
    self.sibling_designs.published.map {|d| {id: d.id, color: d.color, image_url: d.master_img.photo(:small)} if d.master_img.present?}
  end
  
  def all_reviews(country_code)
    return country_code == 'IN' ? self.reviews.approved.with_enabled_designer_reviews.where(:geo => 'domestic') : self.reviews.approved.with_enabled_designer_reviews.where(:geo => 'international')
  end

  def latest_reviews_by_country(country_code)
    return country_code == 'IN' ? (( self.average_rating >= 0.0) ? self.reviews.where(:geo => 'domestic').preload(:design, :designer).product.comments.approved.with_enabled_designer_reviews.random_postive_rating : []) : (( self.average_rating >= 0.0) ? self.reviews.where(:geo => 'international').preload(:design, :designer).product.comments.approved.with_enabled_designer_reviews.random_postive_rating : [])
  end

  private

  def dynamic_size_chart_category
    Rails.cache.fetch("m_dynamic_size_chart_category_#{category_ids.uniq.join(',')}",expires_in: 4.hour) do
      dynamic_size_chart_category = nil
      categories.each do |category|
        dynamic_size_chart_category ||= category.dynamic_size_chart_category
      end
      dynamic_size_chart_category || ''
    end.presence
  end

end
