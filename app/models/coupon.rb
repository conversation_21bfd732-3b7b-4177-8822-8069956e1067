# == Schema Information
#
# Table name: coupons
#
#  id              :integer          not null, primary key
#  name            :string(255)
#  limit           :integer
#  start_date      :datetime
#  end_date        :datetime
#  advertise       :boolean
#  code            :string(255)
#  percent_off     :integer
#  flat_off        :integer
#  min_amount      :integer
#  designer_id     :integer
#  created_at      :datetime         not null
#  updated_at      :datetime         not null
#  coupon_type     :string(255)
#  use_count       :integer
#  notified        :integer
#  source_order_id :integer
#  created_by      :integer
#

class Coupon < ActiveRecord::Base
  has_many :carts
  belongs_to :designer
  belongs_to :account

  # Coupon Types
  COUPON_TYPES = {
    'Flat Discounts' => :FOFF,
    'Percentage Discounts' => :POFF,
    'Credit Discounts' => :COFF,
  }

  scope :advertise, -> { where(advertise: true) }
  scope :live, -> { where('start_date <= ?', Time.zone.now).where('end_date >= ?', Time.zone.now).where('coupons.use_count >= ?', 0).where('coupons.use_count < coupons.limit') }
  scope :designer_coupons, -> { where('designer_id is NOT NULL') }
  scope :for_designer, ->(designer_id) { where(designer_id: designer_id) }
  scope :geo_domestic, -> {where("geo IN ('domestic', NULL, '')")}
  scope :geo_international, -> {where("geo IN ('international', NULL, '')")}
  scope :by_designer, -> {where("coupon_by = 'designer'")}
  scope :by_mirraw, -> {where("coupon_by = 'mirraw'")}
  scope :mirraw_coupons, -> {where("app_name = 'mirraw'")}
  scope :by_source, ->(source) { where("app_source && ARRAY[?]::varchar[]", [source,"all"]) }
  validates :name, presence: true
  validates :code, uniqueness: true
  validates :coupon_type, presence: true, inclusion: { in: %w(FOFF POFF COFF STITOFF SHIPOFF),
    message: "%{value} is not a valid coupon type."}
  validates :limit, presence: true, numericality: { only_integer: true,
    greater_than_or_equal_to: 1 }
  validates :percent_off, numericality: { only_integer: true,
    greater_than_or_equal_to: 0, less_than_or_equal_to: 100 }
  validates :flat_off,  numericality: { only_integer: true,
    greater_than_or_equal_to: 0,
    less_than_or_equal_to: Proc.new { |f| f.min_amount.to_i },
    message: 'should be less than "Min amount"' }, unless: :coff?
  validates :min_amount, presence: true, numericality: { only_integer: true,
    greater_than_or_equal_to: :flat_off,
    message: 'should be greater than "Flat off"' }, unless: :coff?
  validates :use_count, presence: true,  numericality: { only_integer: true,
    less_than_or_equal_to: :limit }
  validate :duration_valid

  # Calculates discount for cart
  #
  # == Parameters:
  #   cart::
  #     Cart Object
  #
  # == Returns:
  # Integer
  #
  def discount_for(cart,return_type = false)
    amount = 0
    if (total = self.eligible_amount(cart,return_type)) > 0
      if poff?
        amount = (self.percent_off * total) / 100
      else
        amount = self.flat_off
      end
    end
    amount.to_i
  end

  def is_stitching?
    coupon_type == "STITOFF"
  end

  def is_shipping?
    coupon_type == "SHIPOFF"
  end

  # Gets cart discountable total in context of coupon conditions
  #
  # == Parameters:
  #   cart::
  #     Cart Object
  #
  # == Returns:
  # Integer
  #
  def running_total(cart,return_type = false)
    if self.designer_id.present?
      if return_type
        cart.designer_item_total(1, self.designer_id)
      else
        cart.designer_item_scaled_total(1, self.designer_id)
      end
    else
      cart.items_total_without_addons(1)
    end
  end

  # Gets amount eligible for discount
  #
  # == Parameters:
  #   cart::
  #     Cart Object
  #
  # == Returns:
  # Integer
  #
  def eligible_amount(cart,return_type = false)
    total = running_total(cart,return_type)
    total -= cart.bmgnx_discounts if poff?
    (total < self.min_amount.to_f || !self.active?(cart)) ? 0 : total
  end

  # Checks whether coupon is active
  #
  # == Returns:
  # Boolean
  #
  def active?(cart = nil)
    if cart && is_cart_timer_coupon?
      CART_COUPON_CONSTANT['Cart_coupon_Enable'] && (api_acc = ApiData.find_by_device_id(cart.device_id)) && api_acc.last_cart_coupon_shown && ((Time.now - api_acc.last_cart_coupon_shown)/60).to_i <= CART_COUPON_CONSTANT['Cart_coupon_valid_Minutes']
    else
      self.valid_period? && (self.use_count < self.limit)
    end
  end

  def valid_period?
    (DateTime.now >= self.start_date) && (DateTime.now < self.end_date)
  end

  def usable?
    self.valid_period? && (self.use_count <= self.limit)
  end

  # Checks whether coupon type is POFF
  #
  # == Returns:
  # Boolean
  #
  def poff?
    self.coupon_type == 'POFF'
  end

  def is_cart_timer_coupon?
    self.name == 'user_cart_coupon'
  end
  # Checks whether coupon type is COFF
  #
  # == Returns:
  # Boolean
  #
  def coff?
    self.coupon_type == 'COFF'
  end

  def self.signup_coupon_for(country_code)
    Rails.cache.fetch("signup_coupon_mobile_#{country_code}", expires_in: 6.hours) do 
      Coupon.live.where(name: "signup_discount_#{country_code}").first || Coupon.live.where(name: 'signup_discount').first
    end
  end

  private

  # Validation for coupon duration
  #
  def duration_valid
    if self.start_date.blank? || self.end_date.blank? ||
      !(self.end_date > self.start_date)
      errors.add(:end_date, 'should be greater than start date')
    end
  end

end
