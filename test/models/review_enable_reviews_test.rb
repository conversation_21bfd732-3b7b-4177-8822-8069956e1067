require 'test_helper'

class ReviewEnableReviewsTest < ActiveSupport::TestCase
  def setup
    @designer_with_reviews = designers(:one)
    @designer_without_reviews = designers(:two)
    @design_with_reviews = designs(:one)
    @design_without_reviews = designs(:two)
    @user = users(:one)
    
    # Set up designers with different enable_reviews flags
    @designer_with_reviews.update_column(:enable_reviews, true)
    @designer_without_reviews.update_column(:enable_reviews, false)
    
    # Associate designs with designers
    @design_with_reviews.update_column(:designer_id, @designer_with_reviews.id)
    @design_without_reviews.update_column(:designer_id, @designer_without_reviews.id)
    
    # Create reviews for both designs
    @review_enabled = Review.create!(
      design: @design_with_reviews,
      designer: @designer_with_reviews,
      user: @user,
      rating: 5,
      review: "Great product!",
      approved: true
    )
    
    @review_disabled = Review.create!(
      design: @design_without_reviews,
      designer: @designer_without_reviews,
      user: @user,
      rating: 4,
      review: "Good product!",
      approved: true
    )
  end

  test "with_enabled_designer_reviews scope filters correctly" do
    enabled_reviews = Review.with_enabled_designer_reviews
    
    assert_includes enabled_reviews, @review_enabled
    assert_not_includes enabled_reviews, @review_disabled
  end

  test "design_ratings only returns reviews for enabled designers" do
    rating, reviews = Review.design_ratings(@design_with_reviews.id, nil)
    
    assert reviews.include?(@review_enabled)
    assert_equal 1, reviews.count
    assert_equal({5 => 1, 4 => 0, 3 => 0, 2 => 0, 1 => 0}, rating)
  end

  test "design_ratings returns empty for disabled designer" do
    rating, reviews = Review.design_ratings(@design_without_reviews.id, nil)
    
    assert_equal 0, reviews.count
    assert_equal({5 => 0, 4 => 0, 3 => 0, 2 => 0, 1 => 0}, rating)
  end

  test "design update_avg_ratings only counts enabled reviews" do
    @design_with_reviews.update_avg_ratings
    @design_without_reviews.update_avg_ratings
    
    assert_equal 1, @design_with_reviews.reload.total_review
    assert_equal 5.0, @design_with_reviews.average_rating
    
    assert_equal 0, @design_without_reviews.reload.total_review
    assert_equal 0.0, @design_without_reviews.average_rating
  end

  test "designer update_avg_ratings respects enable_reviews flag" do
    @designer_with_reviews.update_avg_ratings
    @designer_without_reviews.update_avg_ratings
    
    assert @designer_with_reviews.reload.average_rating > 0
    assert_equal 0, @designer_without_reviews.reload.average_rating
  end

  test "user can_review returns 0 for disabled designer" do
    # Mock the user having purchased the design
    @user.stubs(:orders).returns(
      mock.tap do |orders|
        orders.stubs(:where).returns(
          mock.tap do |filtered_orders|
            filtered_orders.stubs(:includes).returns(
              mock.tap do |included_orders|
                included_orders.stubs(:where).returns(
                  mock.tap do |final_orders|
                    final_orders.stubs(:order).returns(
                      mock.tap do |ordered|
                        ordered.stubs(:last).returns(mock(id: 123))
                      end
                    )
                  end
                )
              end
            )
          end
        )
      end
    )
    
    # Should return 0 for designer with reviews disabled
    assert_equal 0, @user.can_review(@design_without_reviews.id)
  end

  test "user review_for only returns reviews for enabled designers" do
    user_review_enabled = @user.review_for(@design_with_reviews.id)
    user_review_disabled = @user.review_for(@design_without_reviews.id)
    
    assert_equal @review_enabled, user_review_enabled
    assert_nil user_review_disabled
  end
end
